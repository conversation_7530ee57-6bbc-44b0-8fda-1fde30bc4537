import { 
  User, 
  Product, 
  Category, 
  Warehouse, 
  Stock, 
  StockMovement,
  Supplier, 
  Customer, 
  Invoice, 
  InvoiceItem,
  Purchase, 
  PurchaseItem,
  Sale, 
  SaleItem,
  CustomerInteraction,
  UserRole,
  StockMovementType,
  CustomerType,
  InvoiceType,
  InvoiceStatus,
  PurchaseStatus,
  SaleStatus,
  InteractionType
} from '@prisma/client'

// Extended types with relations
export type ProductWithCategory = Product & {
  category: Category
  stocks: (Stock & { warehouse: Warehouse })[]
  _count?: {
    stocks: number
    stockMovements: number
  }
}

export type StockWithDetails = Stock & {
  product: Product & { category: Category }
  warehouse: Warehouse
}

export type StockMovementWithDetails = StockMovement & {
  product: Product
  warehouse: Warehouse
  createdBy: User
}

export type SupplierWithStats = Supplier & {
  _count?: {
    purchases: number
  }
  totalPurchases?: number
}

export type CustomerWithStats = Customer & {
  _count?: {
    invoices: number
    sales: number
    interactions: number
  }
  totalSales?: number
}

export type InvoiceWithDetails = Invoice & {
  customer: Customer
  items: (InvoiceItem & { product: Product })[]
  createdBy: User
}

export type PurchaseWithDetails = Purchase & {
  supplier: Supplier
  items: (PurchaseItem & { product: Product })[]
  createdBy: User
}

export type SaleWithDetails = Sale & {
  customer: Customer
  items: (SaleItem & { product: Product })[]
  createdBy: User
}

export type CustomerInteractionWithDetails = CustomerInteraction & {
  customer: Customer
  createdBy: User
}

// Dashboard types
export interface DashboardStats {
  totalRevenue: number
  totalCustomers: number
  totalProducts: number
  lowStockProducts: number
  pendingInvoices: number
  overdueInvoices: number
  recentSales: SaleWithDetails[]
  topProducts: (Product & { totalSold: number })[]
  monthlyRevenue: { month: string; revenue: number }[]
}

// Form types
export interface ProductFormData {
  name: string
  description?: string
  sku: string
  barcode?: string
  categoryId: string
  costPrice: number
  sellingPrice: number
  minStock: number
  maxStock?: number
}

export interface CustomerFormData {
  name: string
  email?: string
  phone?: string
  address?: string
  taxNumber?: string
  customerType: CustomerType
  notes?: string
}

export interface SupplierFormData {
  name: string
  email?: string
  phone?: string
  address?: string
  taxNumber?: string
  paymentTerms?: string
}

export interface InvoiceFormData {
  customerId: string
  type: InvoiceType
  issueDate: Date
  dueDate?: Date
  items: {
    productId: string
    quantity: number
    unitPrice: number
  }[]
  notes?: string
}

// API Response types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Filter types
export interface ProductFilters {
  search?: string
  categoryId?: string
  lowStock?: boolean
  isActive?: boolean
}

export interface CustomerFilters {
  search?: string
  customerType?: CustomerType
  isActive?: boolean
}

export interface InvoiceFilters {
  search?: string
  status?: InvoiceStatus
  type?: InvoiceType
  customerId?: string
  dateFrom?: Date
  dateTo?: Date
}

// Export all Prisma types
export {
  User,
  Product,
  Category,
  Warehouse,
  Stock,
  StockMovement,
  Supplier,
  Customer,
  Invoice,
  InvoiceItem,
  Purchase,
  PurchaseItem,
  Sale,
  SaleItem,
  CustomerInteraction,
  UserRole,
  StockMovementType,
  CustomerType,
  InvoiceType,
  InvoiceStatus,
  PurchaseStatus,
  SaleStatus,
  InteractionType
}

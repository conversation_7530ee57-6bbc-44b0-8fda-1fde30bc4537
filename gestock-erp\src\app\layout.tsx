import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "GestStock ERP - Gestion d'entreprise pour PME",
  description: "Solution ERP complète pour la gestion des stocks, clients, fournisseurs, facturation et ventes",
  keywords: ["ERP", "gestion", "stock", "facturation", "PME", "CRM"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" className={inter.variable}>
      <body className="font-sans antialiased bg-gray-50 text-gray-900">
        {children}
      </body>
    </html>
  );
}

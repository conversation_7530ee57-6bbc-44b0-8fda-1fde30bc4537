import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administrateur',
      role: 'ADMIN',
    },
  })

  // Create categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { id: 'cat-1' },
      update: {},
      create: {
        id: 'cat-1',
        name: 'Informatique',
        description: 'Ordinateurs, serveurs et équipements informatiques',
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-2' },
      update: {},
      create: {
        id: 'cat-2',
        name: 'Accessoires',
        description: 'Souris, claviers, casques et autres accessoires',
      },
    }),
    prisma.category.upsert({
      where: { id: 'cat-3' },
      update: {},
      create: {
        id: 'cat-3',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description: 'Moniteurs et écrans de toutes tailles',
      },
    }),
  ])

  // Create warehouse
  const warehouse = await prisma.warehouse.upsert({
    where: { id: 'wh-1' },
    update: {},
    create: {
      id: 'wh-1',
      name: 'Entrepôt Principal',
      address: '123 Rue de la Logistique, 75001 Paris',
    },
  })

  // Create products
  const products = await Promise.all([
    prisma.product.upsert({
      where: { sku: 'DELL-LAT-001' },
      update: {},
      create: {
        name: 'Ordinateur portable Dell Latitude 5520',
        description: 'Ordinateur portable professionnel 15.6" Intel Core i5',
        sku: 'DELL-LAT-001',
        barcode: '1234567890123',
        categoryId: categories[0].id,
        costPrice: 800.00,
        sellingPrice: 1200.00,
        minStock: 5,
        maxStock: 50,
        createdById: adminUser.id,
      },
    }),
    prisma.product.upsert({
      where: { sku: 'LOG-MOU-001' },
      update: {},
      create: {
        name: 'Souris sans fil Logitech MX Master 3',
        description: 'Souris ergonomique sans fil avec précision avancée',
        sku: 'LOG-MOU-001',
        barcode: '1234567890124',
        categoryId: categories[1].id,
        costPrice: 25.00,
        sellingPrice: 45.00,
        minStock: 10,
        maxStock: 100,
        createdById: adminUser.id,
      },
    }),
    prisma.product.upsert({
      where: { sku: 'KEY-MEC-001' },
      update: {},
      create: {
        name: 'Clavier mécanique RGB',
        description: 'Clavier mécanique gaming avec rétroéclairage RGB',
        sku: 'KEY-MEC-001',
        barcode: '1234567890125',
        categoryId: categories[1].id,
        costPrice: 80.00,
        sellingPrice: 120.00,
        minStock: 3,
        maxStock: 30,
        createdById: adminUser.id,
      },
    }),
    prisma.product.upsert({
      where: { sku: 'SAM-MON-001' },
      update: {},
      create: {
        name: 'Écran 24 pouces Samsung',
        description: 'Moniteur LED 24" Full HD avec support réglable',
        sku: 'SAM-MON-001',
        barcode: '1234567890126',
        categoryId: categories[2].id,
        costPrice: 200.00,
        sellingPrice: 300.00,
        minStock: 2,
        maxStock: 20,
        createdById: adminUser.id,
      },
    }),
  ])

  // Create stock entries
  await Promise.all([
    prisma.stock.upsert({
      where: { 
        productId_warehouseId: {
          productId: products[0].id,
          warehouseId: warehouse.id,
        }
      },
      update: {},
      create: {
        productId: products[0].id,
        warehouseId: warehouse.id,
        quantity: 15,
      },
    }),
    prisma.stock.upsert({
      where: { 
        productId_warehouseId: {
          productId: products[1].id,
          warehouseId: warehouse.id,
        }
      },
      update: {},
      create: {
        productId: products[1].id,
        warehouseId: warehouse.id,
        quantity: 2,
      },
    }),
    prisma.stock.upsert({
      where: { 
        productId_warehouseId: {
          productId: products[2].id,
          warehouseId: warehouse.id,
        }
      },
      update: {},
      create: {
        productId: products[2].id,
        warehouseId: warehouse.id,
        quantity: 0,
      },
    }),
    prisma.stock.upsert({
      where: { 
        productId_warehouseId: {
          productId: products[3].id,
          warehouseId: warehouse.id,
        }
      },
      update: {},
      create: {
        productId: products[3].id,
        warehouseId: warehouse.id,
        quantity: 8,
      },
    }),
  ])

  // Create suppliers
  const suppliers = await Promise.all([
    prisma.supplier.upsert({
      where: { id: 'sup-1' },
      update: {},
      create: {
        id: 'sup-1',
        name: 'Dell Technologies',
        email: '<EMAIL>',
        phone: '+33 1 23 45 67 89',
        address: '123 Avenue de la Technologie, 92000 Nanterre',
        taxNumber: 'FR12345678901',
        paymentTerms: '30 jours',
        createdById: adminUser.id,
      },
    }),
    prisma.supplier.upsert({
      where: { id: 'sup-2' },
      update: {},
      create: {
        id: 'sup-2',
        name: 'Logitech France',
        email: '<EMAIL>',
        phone: '+33 1 34 56 78 90',
        address: '456 Rue des Accessoires, 75008 Paris',
        taxNumber: 'FR23456789012',
        paymentTerms: '45 jours',
        createdById: adminUser.id,
      },
    }),
  ])

  // Create customers
  const customers = await Promise.all([
    prisma.customer.upsert({
      where: { id: 'cust-1' },
      update: {},
      create: {
        id: 'cust-1',
        name: 'Entreprise ABC',
        email: '<EMAIL>',
        phone: '+33 1 45 67 89 01',
        address: '789 Boulevard du Commerce, 69000 Lyon',
        taxNumber: 'FR34567890123',
        customerType: 'COMPANY',
        notes: 'Client privilégié - remise 10%',
        createdById: adminUser.id,
      },
    }),
    prisma.customer.upsert({
      where: { id: 'cust-2' },
      update: {},
      create: {
        id: 'cust-2',
        name: 'Société XYZ',
        email: '<EMAIL>',
        phone: '+33 1 56 78 90 12',
        address: '321 Place de l\'Innovation, 31000 Toulouse',
        taxNumber: 'FR45678901234',
        customerType: 'COMPANY',
        createdById: adminUser.id,
      },
    }),
  ])

  console.log('✅ Database seeded successfully!')
  console.log(`👤 Created admin user: ${adminUser.email}`)
  console.log(`📦 Created ${categories.length} categories`)
  console.log(`🏭 Created ${products.length} products`)
  console.log(`🏪 Created ${suppliers.length} suppliers`)
  console.log(`👥 Created ${customers.length} customers`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Settings, User, Bell, Shield, Database, Palette, Globe, Mail } from '@/components/ui/icons'

const settingsCategories = [
  {
    title: 'Profil Utilisateur',
    icon: User,
    color: 'text-blue-600',
    settings: [
      { name: 'Informations personnelles', description: 'Nom, email, téléphone' },
      { name: 'Mot de passe', description: 'Changer votre mot de passe' },
      { name: 'Photo de profil', description: 'Avatar et image de profil' },
      { name: 'P<PERSON>f<PERSON>ren<PERSON>', description: 'Langue, fuseau horaire' },
    ]
  },
  {
    title: 'Entreprise',
    icon: Settings,
    color: 'text-green-600',
    settings: [
      { name: 'Informations société', description: 'Nom, adresse, SIRET' },
      { name: 'Logo et branding', description: 'Logo, couleurs, documents' },
      { name: 'Coordonnées bancaires', description: 'RIB, informations de paiement' },
      { name: 'TVA et fiscalité', description: 'Numéro TVA, taux applicables' },
    ]
  },
  {
    title: 'Notifications',
    icon: Bell,
    color: 'text-yellow-600',
    settings: [
      { name: 'Alertes email', description: 'Notifications par email' },
      { name: 'Alertes stock', description: 'Seuils et ruptures de stock' },
      { name: 'Rappels factures', description: 'Échéances et relances' },
      { name: 'Rapports automatiques', description: 'Envoi périodique de rapports' },
    ]
  },
  {
    title: 'Sécurité',
    icon: Shield,
    color: 'text-red-600',
    settings: [
      { name: 'Authentification 2FA', description: 'Double authentification' },
      { name: 'Sessions actives', description: 'Gérer les connexions' },
      { name: 'Historique connexions', description: 'Journal des accès' },
      { name: 'Permissions utilisateurs', description: 'Rôles et droits d\'accès' },
    ]
  },
]

export default function SettingsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Paramètres</h1>
          <p className="text-gray-600">
            Configurez votre application et vos préférences
          </p>
        </div>

        {/* Quick Settings */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Utilisateurs</p>
                  <p className="text-2xl font-bold text-gray-900">5</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Database className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sauvegarde</p>
                  <p className="text-2xl font-bold text-gray-900">Auto</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Globe className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Langue</p>
                  <p className="text-2xl font-bold text-gray-900">FR</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sécurité</p>
                  <p className="text-2xl font-bold text-gray-900">2FA</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {settingsCategories.map((category, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <category.icon className={`mr-2 h-6 w-6 ${category.color}`} />
                  {category.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.settings.map((setting, settingIndex) => (
                    <div key={settingIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div>
                        <h4 className="font-medium text-gray-900">{setting.name}</h4>
                        <p className="text-sm text-gray-600">{setting.description}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        Configurer
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              Configuration Rapide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nom de l'entreprise
                  </label>
                  <Input placeholder="Votre entreprise" defaultValue="GestStock SARL" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email de contact
                  </label>
                  <Input placeholder="<EMAIL>" defaultValue="<EMAIL>" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Devise par défaut
                  </label>
                  <select className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white">
                    <option value="EUR">Euro (€)</option>
                    <option value="USD">Dollar ($)</option>
                    <option value="GBP">Livre (£)</option>
                  </select>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fuseau horaire
                  </label>
                  <select className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white">
                    <option value="Europe/Paris">Europe/Paris (UTC+1)</option>
                    <option value="Europe/London">Europe/London (UTC+0)</option>
                    <option value="America/New_York">America/New_York (UTC-5)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Format de date
                  </label>
                  <select className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white">
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>
                <div className="pt-4">
                  <Button className="w-full">
                    Sauvegarder les modifications
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

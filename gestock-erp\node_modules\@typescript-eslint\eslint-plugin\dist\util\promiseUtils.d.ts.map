{"version": 3, "file": "promiseUtils.d.ts", "sourceRoot": "", "sources": ["../../src/util/promiseUtils.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AAMtE;;;GAGG;AACH,wBAAgB,aAAa,CAC3B,IAAI,EAAE,QAAQ,CAAC,cAAc,EAC7B,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAErC;IACE,WAAW,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9C,UAAU,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,SAAS,CAAC;IAC7C,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;CAC7B,GACD,SAAS,CAqCZ;AAED;;;GAGG;AACH,wBAAgB,cAAc,CAC5B,IAAI,EAAE,QAAQ,CAAC,cAAc,EAC7B,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAErC;IACE,UAAU,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,SAAS,CAAC;IAC7C,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;CAC7B,GACD,SAAS,CAuBZ;AAED;;;GAGG;AACH,wBAAgB,gBAAgB,CAC9B,IAAI,EAAE,QAAQ,CAAC,cAAc,EAC7B,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,GAErC;IACE,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;IAC5B,SAAS,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,SAAS,CAAC;CAC7C,GACD,SAAS,CAsBZ"}
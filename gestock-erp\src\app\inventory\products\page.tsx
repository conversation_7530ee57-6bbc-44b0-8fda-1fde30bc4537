import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Search, 
  Filter,
  Edit,
  Trash2,
  Package,
  AlertTriangle
} from 'lucide-react'

// Mock data - à remplacer par de vraies données
const products = [
  {
    id: '1',
    name: 'Ordinateur portable Dell Latitude',
    sku: 'DELL-LAT-001',
    category: 'Informatique',
    costPrice: 800.00,
    sellingPrice: 1200.00,
    stock: 15,
    minStock: 5,
    status: 'Actif',
  },
  {
    id: '2',
    name: 'Souris sans fil Logitech',
    sku: 'LOG-MOU-001',
    category: 'Accessoires',
    costPrice: 25.00,
    sellingPrice: 45.00,
    stock: 2,
    minStock: 10,
    status: 'Actif',
  },
  {
    id: '3',
    name: 'Clavier mécanique RGB',
    sku: 'KEY-MEC-001',
    category: 'Accessoires',
    costPrice: 80.00,
    sellingPrice: 120.00,
    stock: 0,
    minStock: 3,
    status: 'Actif',
  },
  {
    id: '4',
    name: 'Écran 24 pouces Samsung',
    sku: 'SAM-MON-001',
    category: 'Écrans',
    costPrice: 200.00,
    sellingPrice: 300.00,
    stock: 8,
    minStock: 2,
    status: 'Actif',
  },
]

export default function ProductsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-secondary-900">Gestion des produits</h1>
            <p className="text-secondary-600">
              Gérez votre catalogue de produits et suivez les stocks
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau produit
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-400" />
                <Input
                  placeholder="Rechercher par nom ou SKU..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-primary-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-secondary-600">Total produits</p>
                  <p className="text-2xl font-bold text-secondary-900">{products.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-warning-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-secondary-600">Stock faible</p>
                  <p className="text-2xl font-bold text-warning-600">
                    {products.filter(p => p.stock <= p.minStock && p.stock > 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-danger-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-secondary-600">Rupture stock</p>
                  <p className="text-2xl font-bold text-danger-600">
                    {products.filter(p => p.stock === 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-success-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-secondary-600">Valeur stock</p>
                  <p className="text-2xl font-bold text-success-600">
                    €{products.reduce((acc, p) => acc + (p.costPrice * p.stock), 0).toFixed(0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Products Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des produits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-secondary-200">
                    <th className="text-left py-3 px-4 font-medium text-secondary-600">Produit</th>
                    <th className="text-left py-3 px-4 font-medium text-secondary-600">SKU</th>
                    <th className="text-left py-3 px-4 font-medium text-secondary-600">Catégorie</th>
                    <th className="text-right py-3 px-4 font-medium text-secondary-600">Prix achat</th>
                    <th className="text-right py-3 px-4 font-medium text-secondary-600">Prix vente</th>
                    <th className="text-center py-3 px-4 font-medium text-secondary-600">Stock</th>
                    <th className="text-center py-3 px-4 font-medium text-secondary-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {products.map((product) => (
                    <tr key={product.id} className="border-b border-secondary-100 hover:bg-secondary-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-secondary-900">{product.name}</div>
                      </td>
                      <td className="py-3 px-4 text-secondary-600">{product.sku}</td>
                      <td className="py-3 px-4 text-secondary-600">{product.category}</td>
                      <td className="py-3 px-4 text-right text-secondary-900">
                        €{product.costPrice.toFixed(2)}
                      </td>
                      <td className="py-3 px-4 text-right text-secondary-900">
                        €{product.sellingPrice.toFixed(2)}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          product.stock === 0 
                            ? 'bg-danger-100 text-danger-800'
                            : product.stock <= product.minStock
                            ? 'bg-warning-100 text-warning-800'
                            : 'bg-success-100 text-success-800'
                        }`}>
                          {product.stock}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

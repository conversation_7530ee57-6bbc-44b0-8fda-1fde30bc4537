# 🏢 GestStock ERP - Solution ERP pour PME

Une application ERP complète et moderne conçue spécialement pour les petites et moyennes entreprises. GestStock offre une solution intégrée pour la gestion des stocks, clients, fournisseurs, facturation et ventes.

## ✨ Fonctionnalités

### 📦 Gestion des Stocks
- **Multi-entrepôt** : Gestion de plusieurs entrepôts
- **Suivi en temps réel** : Monitoring des stocks en temps réel
- **Alertes automatiques** : Notifications pour les seuils de stock
- **Mouvements de stock** : Historique complet des entrées/sorties
- **Codes-barres** : Support des codes-barres pour les produits

### 👥 Gestion des Clients (CRM)
- **Fiches clients complètes** : Informations détaillées
- **Historique des interactions** : Suivi des communications
- **Relances automatiques** : Gestion des devis et factures
- **Segmentation** : Classification des clients

### 🏭 Gestion des Fournisseurs
- **Base fournisseurs** : Catalogue complet
- **Historique des achats** : Suivi des commandes
- **Conditions de paiement** : Gestion des termes commerciaux
- **Évaluation** : Notation des fournisseurs

### 📄 Facturation & Devis
- **Création intuitive** : Interface simple et rapide
- **Génération PDF** : Export automatique en PDF
- **Envoi par email** : Diffusion directe aux clients
- **Suivi des statuts** : Monitoring des paiements
- **Avoirs** : Gestion des notes de crédit

### 📊 Tableau de Bord
- **KPIs en temps réel** : Chiffre d'affaires, clients actifs
- **Alertes visuelles** : Produits en rupture, factures en retard
- **Graphiques interactifs** : Analyse des tendances
- **Actions rapides** : Raccourcis vers les fonctions principales

### 🔐 Multi-utilisateur
- **Rôles et permissions** : Admin, vendeur, gestionnaire stock
- **Authentification sécurisée** : Système de connexion robuste
- **Audit trail** : Traçabilité des actions

### 🌍 Multilingue
- **Français** : Interface complète
- **Arabe** : Support RTL
- **Anglais** : Version internationale

## 🛠️ Stack Technologique

### Frontend
- **Next.js 15** - Framework React avec App Router
- **TypeScript** - Typage statique
- **Tailwind CSS** - Framework CSS utilitaire
- **Shadcn/ui** - Composants UI modernes
- **Lucide React** - Icônes
- **Zustand** - Gestion d'état
- **React Hook Form** - Gestion des formulaires
- **Zod** - Validation des données

### Backend
- **Next.js API Routes** - API REST intégrée
- **Prisma** - ORM moderne pour TypeScript
- **PostgreSQL** - Base de données relationnelle

### Infrastructure
- **Vercel** - Hébergement et déploiement
- **Supabase** - Base de données managée (optionnel)

## 🚀 Installation

### Prérequis
- Node.js 18+
- PostgreSQL 14+
- npm ou yarn

### 1. Cloner le projet
```bash
git clone https://github.com/votre-username/gestock-erp.git
cd gestock-erp
```

### 2. Installer les dépendances
```bash
npm install
```

### 3. Configuration de l'environnement
```bash
cp .env.example .env.local
```

Modifiez `.env.local` avec vos paramètres :
```env
DATABASE_URL="postgresql://username:password@localhost:5432/gestock_erp"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Configuration de la base de données
```bash
# Générer le client Prisma
npm run db:generate

# Appliquer les migrations
npm run db:push

# Peupler avec des données de test
npm run db:seed
```

### 5. Lancer l'application
```bash
npm run dev
```

L'application sera accessible sur [http://localhost:3000](http://localhost:3000)

## 📁 Structure du Projet

```
gestock-erp/
├── prisma/
│   ├── schema.prisma      # Schéma de base de données
│   └── seed.ts           # Données de test
├── src/
│   ├── app/              # Pages Next.js (App Router)
│   │   ├── dashboard/    # Tableau de bord
│   │   ├── inventory/    # Gestion des stocks
│   │   ├── customers/    # Gestion des clients
│   │   ├── suppliers/    # Gestion des fournisseurs
│   │   └── invoicing/    # Facturation
│   ├── components/       # Composants React
│   │   ├── ui/          # Composants UI de base
│   │   └── layout/      # Composants de mise en page
│   ├── lib/             # Utilitaires et configuration
│   └── types/           # Types TypeScript
├── public/              # Assets statiques
└── docs/               # Documentation
```

## 🎯 Utilisation

### Connexion
- **Email** : <EMAIL>
- **Mot de passe** : (à configurer lors du premier démarrage)

### Modules Principaux

#### 📊 Tableau de Bord
- Vue d'ensemble des KPIs
- Alertes de stock
- Ventes récentes
- Actions rapides

#### 📦 Gestion des Stocks
- **Produits** : Catalogue complet avec catégories
- **Entrepôts** : Gestion multi-sites
- **Mouvements** : Historique des transactions
- **Inventaires** : Comptages et ajustements

#### 👥 CRM Clients
- **Fiches clients** : Informations complètes
- **Interactions** : Historique des communications
- **Segmentation** : Classification par type

#### 🏭 Fournisseurs
- **Base fournisseurs** : Contacts et conditions
- **Commandes** : Gestion des achats
- **Évaluations** : Performance des fournisseurs

#### 📄 Facturation
- **Devis** : Création et suivi
- **Factures** : Génération et envoi
- **Avoirs** : Notes de crédit

## 🔧 Scripts Disponibles

```bash
# Développement
npm run dev              # Lancer en mode développement
npm run build           # Construire pour la production
npm run start           # Lancer en production
npm run lint            # Vérifier le code

# Base de données
npm run db:generate     # Générer le client Prisma
npm run db:push         # Appliquer les changements de schéma
npm run db:migrate      # Créer une migration
npm run db:studio       # Interface graphique Prisma
npm run db:seed         # Peupler avec des données de test
```

## 🚀 Déploiement

### Vercel (Recommandé)
1. Connectez votre repository GitHub à Vercel
2. Configurez les variables d'environnement
3. Déployez automatiquement

### Docker
```bash
# Construire l'image
docker build -t gestock-erp .

# Lancer le conteneur
docker run -p 3000:3000 gestock-erp
```

### Variables d'Environnement de Production
```env
DATABASE_URL="postgresql://..."
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://your-domain.com"
```

## 🤝 Contribution

1. Fork le projet
2. Créez une branche feature (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📝 Roadmap

### Version 1.1
- [ ] Module de reporting avancé
- [ ] API REST publique
- [ ] Application mobile
- [ ] Intégration e-commerce

### Version 1.2
- [ ] Gestion des projets
- [ ] Comptabilité intégrée
- [ ] Synchronisation bancaire
- [ ] IA pour prédictions de stock

## 🐛 Support

- **Issues** : [GitHub Issues](https://github.com/votre-username/gestock-erp/issues)
- **Documentation** : [Wiki](https://github.com/votre-username/gestock-erp/wiki)
- **Email** : <EMAIL>

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 🙏 Remerciements

- [Next.js](https://nextjs.org/) - Framework React
- [Prisma](https://prisma.io/) - ORM moderne
- [Tailwind CSS](https://tailwindcss.com/) - Framework CSS
- [Shadcn/ui](https://ui.shadcn.com/) - Composants UI
- [Lucide](https://lucide.dev/) - Icônes

---

**GestStock ERP** - Simplifiez la gestion de votre entreprise 🚀

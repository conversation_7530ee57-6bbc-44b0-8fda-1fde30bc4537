import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, TrendingUp, TrendingDown } from '@/components/ui/icons'

const movements = [
  {
    id: '1',
    date: '2024-01-15',
    product: 'Ordinateur portable Dell',
    type: 'IN',
    quantity: 10,
    warehouse: 'Entrepôt Principal',
    reference: 'PUR-2024-001',
    user: '<PERSON>'
  },
  {
    id: '2',
    date: '2024-01-15',
    product: 'Souris Logitech',
    type: 'OUT',
    quantity: 5,
    warehouse: 'Entre<PERSON><PERSON>t Principal',
    reference: 'SAL-2024-001',
    user: '<PERSON>'
  },
  {
    id: '3',
    date: '2024-01-14',
    product: 'Clavier mécanique',
    type: 'TRANSFER',
    quantity: 3,
    warehouse: 'Dép<PERSON>t Lyon',
    reference: 'TRF-2024-001',
    user: '<PERSON>'
  },
  {
    id: '4',
    date: '2024-01-14',
    product: 'Écran Samsung 24"',
    type: 'ADJUSTMENT',
    quantity: -2,
    warehouse: 'Entrepôt Principal',
    reference: 'ADJ-2024-001',
    user: 'Marie Dupont'
  },
]

const getMovementIcon = (type: string) => {
  switch (type) {
    case 'IN':
      return <TrendingUp className="h-4 w-4 text-green-600" />
    case 'OUT':
      return <TrendingDown className="h-4 w-4 text-red-600" />
    case 'TRANSFER':
      return <TrendingUp className="h-4 w-4 text-blue-600" />
    case 'ADJUSTMENT':
      return <TrendingDown className="h-4 w-4 text-orange-600" />
    default:
      return <TrendingUp className="h-4 w-4 text-gray-600" />
  }
}

const getMovementLabel = (type: string) => {
  switch (type) {
    case 'IN': return 'Entrée'
    case 'OUT': return 'Sortie'
    case 'TRANSFER': return 'Transfert'
    case 'ADJUSTMENT': return 'Ajustement'
    default: return type
  }
}

const getMovementColor = (type: string) => {
  switch (type) {
    case 'IN': return 'bg-green-100 text-green-800'
    case 'OUT': return 'bg-red-100 text-red-800'
    case 'TRANSFER': return 'bg-blue-100 text-blue-800'
    case 'ADJUSTMENT': return 'bg-orange-100 text-orange-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function MovementsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mouvements de Stock</h1>
            <p className="text-gray-600">
              Historique complet des entrées, sorties et transferts
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau mouvement
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par produit ou référence..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Entrées aujourd'hui</p>
                  <p className="text-2xl font-bold text-gray-900">15</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingDown className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sorties aujourd'hui</p>
                  <p className="text-2xl font-bold text-gray-900">8</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Transferts</p>
                  <p className="text-2xl font-bold text-gray-900">3</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingDown className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Ajustements</p>
                  <p className="text-2xl font-bold text-gray-900">2</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Movements Table */}
        <Card>
          <CardHeader>
            <CardTitle>Historique des Mouvements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Produit</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Type</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Quantité</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Entrepôt</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Référence</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Utilisateur</th>
                  </tr>
                </thead>
                <tbody>
                  {movements.map((movement) => (
                    <tr key={movement.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 text-gray-900">
                        {new Date(movement.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{movement.product}</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMovementColor(movement.type)}`}>
                          {getMovementIcon(movement.type)}
                          <span className="ml-1">{getMovementLabel(movement.type)}</span>
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <span className={`font-medium ${
                          movement.quantity > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {movement.quantity > 0 ? '+' : ''}{movement.quantity}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{movement.warehouse}</td>
                      <td className="py-3 px-4 text-gray-600">{movement.reference}</td>
                      <td className="py-3 px-4 text-gray-600">{movement.user}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

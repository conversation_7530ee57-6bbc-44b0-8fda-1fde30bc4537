import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, Users, DollarSign, FileText, Clock } from '@/components/ui/icons'

const customers = [
  {
    id: '1',
    name: 'Entreprise ABC',
    email: '<EMAIL>',
    phone: '+33 1 45 67 89 01',
    address: '789 Boulevard du Commerce, 69000 Lyon',
    type: 'Entreprise',
    totalSales: 85430.50,
    invoicesCount: 12,
    lastOrder: '2024-01-15',
    status: 'Actif'
  },
  {
    id: '2',
    name: 'Société XYZ',
    email: '<EMAIL>',
    phone: '+33 1 56 78 90 12',
    address: '321 Place de l\'Innovation, 31000 Toulouse',
    type: 'Entreprise',
    totalSales: 125230.75,
    invoicesCount: 18,
    lastOrder: '2024-01-14',
    status: 'Actif'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+33 1 67 89 01 23',
    address: '456 Rue du Développement, 75011 Paris',
    type: 'SARL',
    totalSales: 67890.25,
    invoicesCount: 9,
    lastOrder: '2024-01-10',
    status: 'Actif'
  },
  {
    id: '4',
    name: 'Jean Dupont',
    email: '<EMAIL>',
    phone: '+33 1 78 90 12 34',
    address: '123 Avenue des Particuliers, 13000 Marseille',
    type: 'Particulier',
    totalSales: 2340.00,
    invoicesCount: 3,
    lastOrder: '2023-12-20',
    status: 'Inactif'
  },
]

export default function CustomersPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Clients</h1>
            <p className="text-gray-600">
              Gérez votre portefeuille clients et leurs interactions
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau client
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total clients</p>
                  <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Clients actifs</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {customers.filter(c => c.status === 'Actif').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">CA total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    €{customers.reduce((acc, c) => acc + c.totalSales, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Factures totales</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {customers.reduce((acc, c) => acc + c.invoicesCount, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customers Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Clients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Contact</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Type</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">CA total</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Factures</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Dernière commande</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {customers.map((customer) => (
                    <tr key={customer.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{customer.name}</div>
                          <div className="text-sm text-gray-500">{customer.address}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-sm text-gray-900">{customer.email}</div>
                          <div className="text-sm text-gray-500">{customer.phone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          customer.type === 'Entreprise' 
                            ? 'bg-blue-100 text-blue-800'
                            : customer.type === 'SARL'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {customer.type}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right text-gray-900">
                        €{customer.totalSales.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-center text-gray-900">
                        {customer.invoicesCount}
                      </td>
                      <td className="py-3 px-4 text-center text-gray-600">
                        {new Date(customer.lastOrder).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          customer.status === 'Actif' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {customer.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

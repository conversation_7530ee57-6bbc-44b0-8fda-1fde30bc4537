import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, ShoppingCart, DollarSign, Clock, Package } from '@/components/ui/icons'

const purchases = [
  {
    id: '1',
    number: 'ACH-2024-001',
    supplier: 'Dell Technologies',
    date: '2024-01-15',
    expectedDate: '2024-01-25',
    amount: 12500.00,
    status: 'Reçu',
    items: 5
  },
  {
    id: '2',
    number: 'ACH-2024-002',
    supplier: 'Logitech France',
    date: '2024-01-14',
    expectedDate: '2024-01-24',
    amount: 4500.50,
    status: 'En cours',
    items: 12
  },
  {
    id: '3',
    number: 'ACH-2024-003',
    supplier: 'Samsung Business',
    date: '2024-01-13',
    expectedDate: '2024-01-23',
    amount: 8900.00,
    status: 'Commandé',
    items: 8
  },
  {
    id: '4',
    number: 'ACH-2024-004',
    supplier: 'HP Enterprise',
    date: '2024-01-10',
    expectedDate: '2024-01-20',
    amount: 6700.00,
    status: 'En retard',
    items: 3
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Reçu': return 'bg-green-100 text-green-800'
    case 'En cours': return 'bg-blue-100 text-blue-800'
    case 'Commandé': return 'bg-yellow-100 text-yellow-800'
    case 'En retard': return 'bg-red-100 text-red-800'
    case 'Annulé': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function PurchasesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Achats</h1>
            <p className="text-gray-600">
              Gérez vos commandes et réceptions fournisseurs
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle commande
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par numéro ou fournisseur..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <ShoppingCart className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total commandes</p>
                  <p className="text-2xl font-bold text-gray-900">{purchases.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Montant total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    €{purchases.reduce((acc, purchase) => acc + purchase.amount, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">En cours</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {purchases.filter(p => p.status === 'En cours' || p.status === 'Commandé').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">En retard</p>
                  <p className="text-2xl font-bold text-red-600">
                    {purchases.filter(p => p.status === 'En retard').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Purchases Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Commandes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Numéro</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Fournisseur</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date commande</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Livraison prévue</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Montant</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Articles</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {purchases.map((purchase) => (
                    <tr key={purchase.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{purchase.number}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-900">{purchase.supplier}</td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(purchase.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(purchase.expectedDate).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-right text-gray-900">
                        €{purchase.amount.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-center text-gray-900">
                        {purchase.items}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(purchase.status)}`}>
                          {purchase.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

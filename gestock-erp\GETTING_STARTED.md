# 🚀 Guide de Démarrage Rapide - GestStock ERP

Ce guide vous accompagne pour configurer et lancer votre application ERP en quelques minutes.

## 🎯 **Démonstration Immédiate**

**Pour voir l'interface immédiatement :**
1. Ouvrez le fichier `demo.html` dans votre navigateur
2. Explorez l'interface du tableau de bord
3. Découvrez les fonctionnalités principales

## 📋 Prérequis (Pour le développement)

Avant de commencer le développement, assurez-vous d'avoir installé :

- **Node.js 18+** : [Télécharger Node.js](https://nodejs.org/)
- **PostgreSQL 14+** : [Télécharger PostgreSQL](https://www.postgresql.org/download/) (optionnel)
- **Git** : [Télécharger Git](https://git-scm.com/)

## ⚡ Installation Express

### 1. Cloner et installer
```bash
# Cloner le projet
git clone https://github.com/votre-username/gestock-erp.git
cd gestock-erp

# Installer les dépendances
npm install
```

### 2. Configuration rapide avec Supabase (Recommandé)

Pour un démarrage ultra-rapide, utilisez Supabase comme base de données managée :

1. **Créer un compte Supabase** : [supabase.com](https://supabase.com)
2. **Créer un nouveau projet**
3. **Récupérer les clés** dans Settings > API

```bash
# Copier le fichier d'environnement
cp .env.example .env.local
```

Modifier `.env.local` :
```env
# Remplacer par vos clés Supabase
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
NEXT_PUBLIC_SUPABASE_URL=https://[YOUR-PROJECT-REF].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=[YOUR-ANON-KEY]

# Générer une clé secrète
NEXTAUTH_SECRET="your-super-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
```

### 3. Initialiser la base de données
```bash
# Générer le client Prisma
npm run db:generate

# Créer les tables
npm run db:push

# Peupler avec des données de test
npm run db:seed
```

### 4. Lancer l'application
```bash
npm run dev
```

🎉 **Votre ERP est prêt !** Ouvrez [http://localhost:3000](http://localhost:3000)

## 🔐 Première Connexion

Utilisez ces identifiants de test :
- **Email** : <EMAIL>
- **Rôle** : Administrateur

## 🎯 Premiers Pas

### 1. Explorer le Tableau de Bord
- Vue d'ensemble des KPIs
- Alertes de stock
- Ventes récentes

### 2. Ajouter vos Produits
1. Aller dans **Gestion des stocks > Produits**
2. Cliquer sur **Nouveau produit**
3. Remplir les informations essentielles

### 3. Configurer vos Entrepôts
1. Aller dans **Gestion des stocks > Entrepôts**
2. Ajouter vos sites de stockage

### 4. Importer vos Clients
1. Aller dans **Clients**
2. Ajouter vos contacts existants

## 🛠️ Configuration Avancée

### Base de Données Locale (PostgreSQL)

Si vous préférez une installation locale :

```bash
# Créer la base de données
createdb gestock_erp

# Configurer .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/gestock_erp"
```

### Variables d'Environnement

```env
# Base de données
DATABASE_URL="postgresql://..."

# Authentification
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Application
NEXT_PUBLIC_APP_NAME="GestStock ERP"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# Email (optionnel)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

## 📊 Données de Test

L'application inclut des données de démonstration :

- **4 produits** avec différents niveaux de stock
- **2 fournisseurs** (Dell, Logitech)
- **2 clients** (Entreprise ABC, Société XYZ)
- **1 entrepôt** principal
- **Mouvements de stock** d'exemple

## 🔧 Commandes Utiles

```bash
# Développement
npm run dev              # Serveur de développement
npm run build           # Build de production
npm run start           # Serveur de production

# Base de données
npm run db:studio       # Interface graphique Prisma
npm run db:migrate      # Créer une migration
npm run db:reset        # Réinitialiser la DB

# Qualité du code
npm run lint            # Vérifier le code
npm run type-check      # Vérifier les types
```

## 🚨 Résolution de Problèmes

### Erreur de connexion à la base
```bash
# Vérifier que PostgreSQL est démarré
sudo service postgresql start

# Tester la connexion
psql -h localhost -U username -d gestock_erp
```

### Erreur de dépendances
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
```

### Port déjà utilisé
```bash
# Changer le port dans package.json
"dev": "next dev -p 3001"
```

## 📞 Support

- **Documentation** : [Wiki du projet](https://github.com/votre-username/gestock-erp/wiki)
- **Issues** : [GitHub Issues](https://github.com/votre-username/gestock-erp/issues)
- **Email** : <EMAIL>

## 🎯 Prochaines Étapes

1. **Personnaliser** les paramètres de l'entreprise
2. **Importer** vos données existantes
3. **Former** vos équipes
4. **Configurer** les sauvegardes
5. **Planifier** la mise en production

---

**Félicitations !** Vous êtes prêt à utiliser GestStock ERP 🎉

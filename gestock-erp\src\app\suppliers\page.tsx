import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, UserCheck, DollarSign, ShoppingCart } from '@/components/ui/icons'

const suppliers = [
  {
    id: '1',
    name: 'Dell Technologies',
    email: '<EMAIL>',
    phone: '+33 1 23 45 67 89',
    address: '123 Avenue de la Technologie, 92000 Nanterre',
    paymentTerms: '30 jours',
    totalPurchases: 125430.50,
    ordersCount: 15,
    status: 'Actif'
  },
  {
    id: '2',
    name: 'Logitech France',
    email: '<EMAIL>',
    phone: '+33 1 34 56 78 90',
    address: '456 Rue des Accessoires, 75008 Paris',
    paymentTerms: '45 jours',
    totalPurchases: 45230.75,
    ordersCount: 8,
    status: 'Actif'
  },
  {
    id: '3',
    name: 'Samsung Business',
    email: '<EMAIL>',
    phone: '+33 1 45 67 89 01',
    address: '789 Boulevard de l\'Innovation, 69000 Lyon',
    paymentTerms: '60 jours',
    totalPurchases: 89650.25,
    ordersCount: 12,
    status: 'Actif'
  },
  {
    id: '4',
    name: 'HP Enterprise',
    email: '<EMAIL>',
    phone: '+33 1 56 78 90 12',
    address: '321 Place du Commerce, 31000 Toulouse',
    paymentTerms: '30 jours',
    totalPurchases: 67890.00,
    ordersCount: 6,
    status: 'Inactif'
  },
]

export default function SuppliersPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Fournisseurs</h1>
            <p className="text-gray-600">
              Gérez vos partenaires commerciaux et leurs conditions
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau fournisseur
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par nom ou email..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total fournisseurs</p>
                  <p className="text-2xl font-bold text-gray-900">{suppliers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Fournisseurs actifs</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {suppliers.filter(s => s.status === 'Actif').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total achats</p>
                  <p className="text-2xl font-bold text-gray-900">
                    €{suppliers.reduce((acc, s) => acc + s.totalPurchases, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <ShoppingCart className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Commandes totales</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {suppliers.reduce((acc, s) => acc + s.ordersCount, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Suppliers Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Fournisseurs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Fournisseur</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Contact</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Conditions</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Total achats</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Commandes</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {suppliers.map((supplier) => (
                    <tr key={supplier.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">{supplier.name}</div>
                          <div className="text-sm text-gray-500">{supplier.address}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-sm text-gray-900">{supplier.email}</div>
                          <div className="text-sm text-gray-500">{supplier.phone}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{supplier.paymentTerms}</td>
                      <td className="py-3 px-4 text-right text-gray-900">
                        €{supplier.totalPurchases.toLocaleString()}
                      </td>
                      <td className="py-3 px-4 text-center text-gray-900">
                        {supplier.ordersCount}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          supplier.status === 'Actif' 
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {supplier.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

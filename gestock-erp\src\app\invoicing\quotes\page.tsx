import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, FileText, Clock, DollarSign } from '@/components/ui/icons'

const quotes = [
  {
    id: '1',
    number: 'DEV-2024-001',
    customer: 'Entreprise ABC',
    date: '2024-01-15',
    validUntil: '2024-02-14',
    amount: 3250.00,
    status: 'Accepté',
  },
  {
    id: '2',
    number: 'DEV-2024-002',
    customer: 'Société XYZ',
    date: '2024-01-14',
    validUntil: '2024-02-13',
    amount: 1890.50,
    status: 'En attente',
  },
  {
    id: '3',
    number: 'DEV-2024-003',
    customer: 'SARL Martin',
    date: '2024-01-13',
    validUntil: '2024-02-12',
    amount: 4100.00,
    status: 'Envoyé',
  },
  {
    id: '4',
    number: 'DEV-2024-004',
    customer: 'Nouvelle Entreprise',
    date: '2024-01-10',
    validUntil: '2024-01-25',
    amount: 2340.00,
    status: 'Expiré',
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Accepté': return 'bg-green-100 text-green-800'
    case 'En attente': return 'bg-yellow-100 text-yellow-800'
    case 'Envoyé': return 'bg-blue-100 text-blue-800'
    case 'Expiré': return 'bg-red-100 text-red-800'
    case 'Refusé': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function QuotesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Devis</h1>
            <p className="text-gray-600">
              Créez et suivez vos devis clients
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouveau devis
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par numéro ou client..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total devis</p>
                  <p className="text-2xl font-bold text-gray-900">{quotes.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Valeur totale</p>
                  <p className="text-2xl font-bold text-gray-900">
                    €{quotes.reduce((acc, quote) => acc + quote.amount, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">En attente</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {quotes.filter(q => q.status === 'En attente').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Acceptés</p>
                  <p className="text-2xl font-bold text-green-600">
                    {quotes.filter(q => q.status === 'Accepté').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quotes Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Devis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Numéro</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Validité</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Montant</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {quotes.map((quote) => (
                    <tr key={quote.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{quote.number}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-900">{quote.customer}</td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(quote.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(quote.validUntil).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-right text-gray-900">
                        €{quote.amount.toFixed(2)}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(quote.status)}`}>
                          {quote.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

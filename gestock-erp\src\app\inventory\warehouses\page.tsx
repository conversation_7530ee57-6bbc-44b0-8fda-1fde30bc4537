import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Plus, Warehouse, Package, AlertTriangle } from '@/components/ui/icons'

const warehouses = [
  { 
    id: '1', 
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> Principal', 
    address: '123 Rue de la Logistique, 75001 Paris',
    products: 856,
    capacity: 1000,
    alerts: 12
  },
  { 
    id: '2', 
    name: 'Dép<PERSON>t Lyon', 
    address: '456 Avenue du Commerce, 69000 Lyon',
    products: 234,
    capacity: 500,
    alerts: 3
  },
  { 
    id: '3', 
    name: 'Stock Marseille', 
    address: '789 Boulevard du Port, 13000 Marseille',
    products: 145,
    capacity: 300,
    alerts: 8
  },
]

export default function WarehousesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Entrepôts</h1>
            <p className="text-gray-600">
              <PERSON><PERSON><PERSON> vos emplacements de stockage et leur capacité
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouvel entrepôt
          </Button>
        </div>

        {/* Warehouses Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {warehouses.map((warehouse) => (
            <Card key={warehouse.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Warehouse className="mr-2 h-5 w-5 text-blue-600" />
                  {warehouse.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">{warehouse.address}</p>
                  
                  {/* Capacity Bar */}
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Capacité utilisée</span>
                      <span>{warehouse.products}/{warehouse.capacity}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          (warehouse.products / warehouse.capacity) > 0.8 
                            ? 'bg-red-600' 
                            : (warehouse.products / warehouse.capacity) > 0.6 
                            ? 'bg-yellow-600' 
                            : 'bg-green-600'
                        }`}
                        style={{ width: `${(warehouse.products / warehouse.capacity) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <Package className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                      <p className="text-lg font-bold text-gray-900">{warehouse.products}</p>
                      <p className="text-xs text-gray-600">Produits</p>
                    </div>
                    <div className="text-center">
                      <Warehouse className="h-6 w-6 text-green-600 mx-auto mb-1" />
                      <p className="text-lg font-bold text-gray-900">
                        {Math.round((warehouse.products / warehouse.capacity) * 100)}%
                      </p>
                      <p className="text-xs text-gray-600">Utilisé</p>
                    </div>
                    <div className="text-center">
                      <AlertTriangle className="h-6 w-6 text-red-600 mx-auto mb-1" />
                      <p className="text-lg font-bold text-gray-900">{warehouse.alerts}</p>
                      <p className="text-xs text-gray-600">Alertes</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 pt-4">
                    <Button variant="outline" size="sm" className="flex-1">
                      Voir détails
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      Gérer stock
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Summary Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Résumé Global</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-3xl font-bold text-blue-600">{warehouses.length}</p>
                <p className="text-gray-600">Entrepôts actifs</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-green-600">
                  {warehouses.reduce((acc, w) => acc + w.products, 0)}
                </p>
                <p className="text-gray-600">Total produits stockés</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-purple-600">
                  {warehouses.reduce((acc, w) => acc + w.capacity, 0)}
                </p>
                <p className="text-gray-600">Capacité totale</p>
              </div>
              <div className="text-center">
                <p className="text-3xl font-bold text-red-600">
                  {warehouses.reduce((acc, w) => acc + w.alerts, 0)}
                </p>
                <p className="text-gray-600">Alertes actives</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

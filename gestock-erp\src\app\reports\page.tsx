import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BarChart3, TrendingUp, DollarSign, Package, Users, FileText, Download } from '@/components/ui/icons'

const reportCategories = [
  {
    title: 'Rapports Financiers',
    icon: DollarSign,
    color: 'text-green-600',
    reports: [
      { name: '<PERSON><PERSON><PERSON> d\'affaires', description: 'CA par période, client, produit' },
      { name: 'Rentabilité', description: 'Marges et bénéfices par produit' },
      { name: 'Créances clients', description: 'Factures impayées et relances' },
      { name: 'Dettes fournisseurs', description: 'Factures à payer' },
    ]
  },
  {
    title: 'Rapports de Stock',
    icon: Package,
    color: 'text-blue-600',
    reports: [
      { name: 'Valorisation stock', description: 'Valeur du stock par entrepôt' },
      { name: 'Rotation des stocks', description: 'Analyse de la rotation' },
      { name: 'Ruptures de stock', description: 'Produits en rupture' },
      { name: 'Mouvements', description: 'Historique des mouvements' },
    ]
  },
  {
    title: 'Rapports Commerciaux',
    icon: TrendingUp,
    color: 'text-purple-600',
    reports: [
      { name: 'Performance ventes', description: 'Ventes par vendeur/période' },
      { name: 'Analyse clients', description: 'Top clients et segmentation' },
      { name: 'Produits populaires', description: 'Meilleures ventes' },
      { name: 'Devis/Factures', description: 'Taux de conversion' },
    ]
  },
  {
    title: 'Rapports Achats',
    icon: Users,
    color: 'text-orange-600',
    reports: [
      { name: 'Performance fournisseurs', description: 'Délais et qualité' },
      { name: 'Analyse des coûts', description: 'Évolution des prix d\'achat' },
      { name: 'Commandes en cours', description: 'Suivi des livraisons' },
      { name: 'Comparatif fournisseurs', description: 'Analyse comparative' },
    ]
  },
]

export default function ReportsPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Rapports et Analyses</h1>
            <p className="text-gray-600">
              Analysez vos performances avec des rapports détaillés
            </p>
          </div>
          <Button className="flex items-center">
            <Download className="mr-2 h-4 w-4" />
            Exporter données
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rapports générés</p>
                  <p className="text-2xl font-bold text-gray-900">156</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">CA ce mois</p>
                  <p className="text-2xl font-bold text-gray-900">€125,430</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Valeur stock</p>
                  <p className="text-2xl font-bold text-gray-900">€89,250</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Clients actifs</p>
                  <p className="text-2xl font-bold text-gray-900">234</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {reportCategories.map((category, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <category.icon className={`mr-2 h-6 w-6 ${category.color}`} />
                  {category.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.reports.map((report, reportIndex) => (
                    <div key={reportIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div>
                        <h4 className="font-medium text-gray-900">{report.name}</h4>
                        <p className="text-sm text-gray-600">{report.description}</p>
                      </div>
                      <Button variant="outline" size="sm">
                        Générer
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              Rapports Récents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Rapport CA Janvier 2024</p>
                    <p className="text-sm text-gray-600">Généré le 15/01/2024 à 14:30</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Télécharger
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <Package className="h-5 w-5 text-green-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Valorisation Stock</p>
                    <p className="text-sm text-gray-600">Généré le 14/01/2024 à 09:15</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Télécharger
                </Button>
              </div>
              
              <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-purple-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Top Clients Q4 2023</p>
                    <p className="text-sm text-gray-600">Généré le 12/01/2024 à 16:45</p>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Télécharger
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

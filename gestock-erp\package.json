{"name": "gestock-erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "next": "15.3.3", "@prisma/client": "^5.22.0", "prisma": "^5.22.0", "lucide-react": "^0.460.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "class-variance-authority": "^0.7.1"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}
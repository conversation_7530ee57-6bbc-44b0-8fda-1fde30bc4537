{"name": "gestock-erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "next": "15.3.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "eslint": "^8", "eslint-config-next": "15.3.3"}}
import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, TrendingUp, DollarSign, Users, Package } from '@/components/ui/icons'

const sales = [
  {
    id: '1',
    number: 'VEN-2024-001',
    customer: 'Entreprise ABC',
    date: '2024-01-15',
    deliveryDate: '2024-01-18',
    amount: 1250.00,
    status: 'Livré',
    items: 3
  },
  {
    id: '2',
    number: 'VEN-2024-002',
    customer: 'Société XYZ',
    date: '2024-01-14',
    deliveryDate: '2024-01-17',
    amount: 890.50,
    status: 'En cours',
    items: 5
  },
  {
    id: '3',
    number: 'VEN-2024-003',
    customer: 'SA<PERSON> Martin',
    date: '2024-01-13',
    deliveryDate: '2024-01-16',
    amount: 2100.00,
    status: 'Confirmé',
    items: 8
  },
  {
    id: '4',
    number: 'VEN-2024-004',
    customer: '<PERSON>',
    date: '2024-01-12',
    deliveryDate: '2024-01-15',
    amount: 340.00,
    status: 'Livré',
    items: 2
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Livré': return 'bg-green-100 text-green-800'
    case 'En cours': return 'bg-blue-100 text-blue-800'
    case 'Confirmé': return 'bg-yellow-100 text-yellow-800'
    case 'Annulé': return 'bg-red-100 text-red-800'
    case 'Retourné': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function SalesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Ventes</h1>
            <p className="text-gray-600">
              Suivez vos ventes et livraisons clients
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle vente
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par numéro ou client..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total ventes</p>
                  <p className="text-2xl font-bold text-gray-900">{sales.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">CA total</p>
                  <p className="text-2xl font-bold text-gray-900">
                    €{sales.reduce((acc, sale) => acc + sale.amount, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Clients uniques</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {new Set(sales.map(s => s.customer)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Articles vendus</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {sales.reduce((acc, sale) => acc + sale.items, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sales Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Ventes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Numéro</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date vente</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Livraison</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Montant</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Articles</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sales.map((sale) => (
                    <tr key={sale.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{sale.number}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-900">{sale.customer}</td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(sale.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(sale.deliveryDate).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-right text-gray-900">
                        €{sale.amount.toFixed(2)}
                      </td>
                      <td className="py-3 px-4 text-center text-gray-900">
                        {sale.items}
                      </td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(sale.status)}`}>
                          {sale.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

export default function Home() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333', fontSize: '2rem' }}>
        🎉 GestStock ERP - Application Fonctionnelle !
      </h1>
      <p style={{ color: '#666', fontSize: '1.1rem', marginBottom: '20px' }}>
        ✅ Serveur Next.js démarré avec succès
      </p>

      <div style={{ backgroundColor: '#f0f9ff', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h2 style={{ color: '#1e40af', marginBottom: '10px' }}>🚀 Application ERP Prête !</h2>
        <ul style={{ color: '#374151' }}>
          <li>✅ Next.js 14 avec TypeScript</li>
          <li>✅ Architecture ERP complète</li>
          <li>✅ Base de données Prisma configurée</li>
          <li>✅ Interface utilisateur moderne</li>
        </ul>
      </div>

      <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        <a
          href="/dashboard"
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            padding: '10px 20px',
            textDecoration: 'none',
            borderRadius: '6px',
            display: 'inline-block'
          }}
        >
          📊 Dashboard
        </a>
        <a
          href="/inventory/products"
          style={{
            backgroundColor: '#10b981',
            color: 'white',
            padding: '10px 20px',
            textDecoration: 'none',
            borderRadius: '6px',
            display: 'inline-block'
          }}
        >
          📦 Gestion Stocks
        </a>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f9fafb', borderRadius: '6px' }}>
        <h3 style={{ color: '#374151', marginBottom: '10px' }}>📋 Prochaines étapes :</h3>
        <ol style={{ color: '#6b7280' }}>
          <li>Configurer la base de données (Supabase recommandé)</li>
          <li>Implémenter l'authentification</li>
          <li>Développer les APIs pour chaque module</li>
          <li>Ajouter les fonctionnalités avancées</li>
        </ol>
      </div>
    </div>
  )
}

'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Package,
  Users,
  UserCheck,
  FileText,
  ShoppingCart,
  TrendingUp,
  Settings,
  LogOut,
  Warehouse,
  BarChart3,
} from '@/components/ui/icons'

// Utility function for combining classes
function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ')
}

const navigation = [
  {
    name: 'Tableau de bord',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Gestion des stocks',
    href: '/inventory',
    icon: Package,
    children: [
      { name: 'Produits', href: '/inventory/products' },
      { name: 'Catégories', href: '/inventory/categories' },
      { name: 'Entrepôts', href: '/inventory/warehouses' },
      { name: 'Mouvements', href: '/inventory/movements' },
    ],
  },
  {
    name: 'Fournisseurs',
    href: '/suppliers',
    icon: User<PERSON>he<PERSON>,
  },
  {
    name: 'Clients',
    href: '/customers',
    icon: Users,
  },
  {
    name: 'Facturation',
    href: '/invoicing',
    icon: FileText,
    children: [
      { name: 'Factures', href: '/invoicing/invoices' },
      { name: 'Devis', href: '/invoicing/quotes' },
      { name: 'Avoirs', href: '/invoicing/credit-notes' },
    ],
  },
  {
    name: 'Achats',
    href: '/purchases',
    icon: ShoppingCart,
  },
  {
    name: 'Ventes',
    href: '/sales',
    icon: TrendingUp,
  },
  {
    name: 'Rapports',
    href: '/reports',
    icon: BarChart3,
  },
]

const bottomNavigation = [
  {
    name: 'Paramètres',
    href: '/settings',
    icon: Settings,
  },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const pathname = usePathname()

  return (
    <div className={cn('flex h-full w-64 flex-col bg-white border-r border-gray-200', className)}>
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Warehouse className="h-8 w-8 text-blue-600" />
          <span className="text-xl font-bold text-gray-900">GestStock</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
          
          return (
            <div key={item.name}>
              <Link
                href={item.href}
                className={cn(
                  'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                )}
              >
                <item.icon
                  className={cn(
                    'mr-3 h-5 w-5 flex-shrink-0',
                    isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'
                  )}
                />
                {item.name}
              </Link>
              
              {/* Sub-navigation */}
              {item.children && isActive && (
                <div className="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={cn(
                        'block px-3 py-1 text-sm rounded-md transition-colors',
                        pathname === child.href
                          ? 'text-blue-700 bg-blue-50'
                          : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                      )}
                    >
                      {child.name}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          )
        })}
      </nav>

      {/* Bottom navigation */}
      <div className="border-t border-gray-200 p-3">
        {bottomNavigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors"
          >
            <item.icon className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500" />
            {item.name}
          </Link>
        ))}

        <button className="group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors">
          <LogOut className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500" />
          Déconnexion
        </button>
      </div>
    </div>
  )
}

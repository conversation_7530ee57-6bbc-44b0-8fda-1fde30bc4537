import { MainLayout } from '@/components/layout/main-layout'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Filter, Edit, Trash2, FileText, DollarSign } from '@/components/ui/icons'

const creditNotes = [
  {
    id: '1',
    number: 'AVO-2024-001',
    customer: 'Entreprise ABC',
    originalInvoice: 'FAC-2024-001',
    date: '2024-01-16',
    amount: 125.00,
    reason: 'Produit défectueux',
    status: 'Validé',
  },
  {
    id: '2',
    number: 'AVO-2024-002',
    customer: 'Société XYZ',
    originalInvoice: 'FAC-2024-015',
    date: '2024-01-14',
    amount: 89.50,
    reason: 'Erreur de facturation',
    status: 'En attente',
  },
  {
    id: '3',
    number: 'AVO-2024-003',
    customer: 'SA<PERSON> Martin',
    originalInvoice: 'FAC-2024-023',
    date: '2024-01-12',
    amount: 210.00,
    reason: 'Retour marchandise',
    status: 'Validé',
  },
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Validé': return 'bg-green-100 text-green-800'
    case 'En attente': return 'bg-yellow-100 text-yellow-800'
    case 'Annulé': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export default function CreditNotesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Avoirs</h1>
            <p className="text-gray-600">
              Gérez les notes de crédit et remboursements
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouvel avoir
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher par numéro ou client..."
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total avoirs</p>
                  <p className="text-2xl font-bold text-gray-900">{creditNotes.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Montant total</p>
                  <p className="text-2xl font-bold text-red-600">
                    -€{creditNotes.reduce((acc, note) => acc + note.amount, 0).toFixed(2)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Validés</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {creditNotes.filter(note => note.status === 'Validé').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Credit Notes Table */}
        <Card>
          <CardHeader>
            <CardTitle>Liste des Avoirs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Numéro</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Facture origine</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Date</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-600">Montant</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-600">Motif</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Statut</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {creditNotes.map((note) => (
                    <tr key={note.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-gray-900">{note.number}</div>
                      </td>
                      <td className="py-3 px-4 text-gray-900">{note.customer}</td>
                      <td className="py-3 px-4 text-blue-600 hover:text-blue-800 cursor-pointer">
                        {note.originalInvoice}
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {new Date(note.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="py-3 px-4 text-right text-red-600 font-medium">
                        -€{note.amount.toFixed(2)}
                      </td>
                      <td className="py-3 px-4 text-gray-600">{note.reason}</td>
                      <td className="py-3 px-4 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(note.status)}`}>
                          {note.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-center">
                        <div className="flex justify-center space-x-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

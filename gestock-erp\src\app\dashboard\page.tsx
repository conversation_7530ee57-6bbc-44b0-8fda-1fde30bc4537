import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  TrendingUp, 
  Users, 
  Package, 
  AlertTriangle,
  FileText,
  Clock,
  DollarSign,
  ShoppingCart
} from 'lucide-react'

// Mock data - à remplacer par de vraies données de la base
const stats = [
  {
    title: 'Chi<PERSON>re d\'affaires',
    value: '€45,231',
    change: '+20.1%',
    changeType: 'positive' as const,
    icon: DollarSign,
  },
  {
    title: 'Clients actifs',
    value: '2,350',
    change: '+180',
    changeType: 'positive' as const,
    icon: Users,
  },
  {
    title: 'Produits en stock',
    value: '12,234',
    change: '-19',
    changeType: 'negative' as const,
    icon: Package,
  },
  {
    title: 'Alertes stock',
    value: '23',
    change: '+4',
    changeType: 'warning' as const,
    icon: AlertTriangle,
  },
]

const recentSales = [
  {
    id: 'SAL-2024-001',
    customer: 'Entreprise ABC',
    amount: 1250.00,
    status: 'Livré',
    date: '2024-01-15',
  },
  {
    id: 'SAL-2024-002',
    customer: 'Société XYZ',
    amount: 890.50,
    status: 'En cours',
    date: '2024-01-14',
  },
  {
    id: 'SAL-2024-003',
    customer: 'SARL Martin',
    amount: 2100.00,
    status: 'Livré',
    date: '2024-01-13',
  },
]

const lowStockProducts = [
  {
    name: 'Ordinateur portable Dell',
    sku: 'DELL-LAP-001',
    currentStock: 2,
    minStock: 5,
  },
  {
    name: 'Souris sans fil Logitech',
    sku: 'LOG-MOU-001',
    currentStock: 1,
    minStock: 10,
  },
  {
    name: 'Clavier mécanique',
    sku: 'KEY-MEC-001',
    currentStock: 0,
    minStock: 3,
  },
]

export default function DashboardPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Tableau de bord</h1>
          <p className="text-secondary-600">
            Vue d'ensemble de votre activité commerciale
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-secondary-600">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-secondary-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-secondary-900">{stat.value}</div>
                <p className={`text-xs ${
                  stat.changeType === 'positive' 
                    ? 'text-success-600' 
                    : stat.changeType === 'negative'
                    ? 'text-danger-600'
                    : 'text-warning-600'
                }`}>
                  {stat.change} par rapport au mois dernier
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Sales */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="mr-2 h-5 w-5" />
                Ventes récentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-secondary-900">{sale.customer}</p>
                      <p className="text-sm text-secondary-500">{sale.id}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-secondary-900">
                        €{sale.amount.toFixed(2)}
                      </p>
                      <p className={`text-xs ${
                        sale.status === 'Livré' ? 'text-success-600' : 'text-warning-600'
                      }`}>
                        {sale.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Low Stock Alert */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="mr-2 h-5 w-5 text-warning-600" />
                Alertes de stock
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {lowStockProducts.map((product) => (
                  <div key={product.sku} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-secondary-900">{product.name}</p>
                      <p className="text-sm text-secondary-500">{product.sku}</p>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${
                        product.currentStock === 0 
                          ? 'text-danger-600' 
                          : 'text-warning-600'
                      }`}>
                        {product.currentStock} / {product.minStock}
                      </p>
                      <p className="text-xs text-secondary-500">
                        {product.currentStock === 0 ? 'Rupture' : 'Stock faible'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions rapides</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="flex flex-col items-center p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <FileText className="h-8 w-8 text-primary-600 mb-2" />
                <span className="text-sm font-medium">Nouvelle facture</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <ShoppingCart className="h-8 w-8 text-primary-600 mb-2" />
                <span className="text-sm font-medium">Nouvel achat</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <Package className="h-8 w-8 text-primary-600 mb-2" />
                <span className="text-sm font-medium">Ajouter produit</span>
              </button>
              <button className="flex flex-col items-center p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors">
                <Users className="h-8 w-8 text-primary-600 mb-2" />
                <span className="text-sm font-medium">Nouveau client</span>
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/invoicing/credit-notes/page";
exports.ids = ["app/invoicing/credit-notes/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finvoicing%2Fcredit-notes%2Fpage&page=%2Finvoicing%2Fcredit-notes%2Fpage&appPaths=%2Finvoicing%2Fcredit-notes%2Fpage&pagePath=private-next-app-dir%2Finvoicing%2Fcredit-notes%2Fpage.tsx&appDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finvoicing%2Fcredit-notes%2Fpage&page=%2Finvoicing%2Fcredit-notes%2Fpage&appPaths=%2Finvoicing%2Fcredit-notes%2Fpage&pagePath=private-next-app-dir%2Finvoicing%2Fcredit-notes%2Fpage.tsx&appDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'invoicing',\n        {\n        children: [\n        'credit-notes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invoicing/credit-notes/page.tsx */ \"(rsc)/./src/app/invoicing/credit-notes/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/invoicing/credit-notes/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/invoicing/credit-notes/page\",\n        pathname: \"/invoicing/credit-notes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finvoicing%2Fcredit-notes%2Fpage&page=%2Finvoicing%2Fcredit-notes%2Fpage&appPaths=%2Finvoicing%2Fcredit-notes%2Fpage&pagePath=private-next-app-dir%2Finvoicing%2Fcredit-notes%2Fpage.tsx&appDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/main-layout.tsx */ \"(ssr)/./src/components/layout/main-layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2lzc2FtJTVDJTVDLnZzY29kZSU1QyU1Q2dlc3RvY2slNUMlNUNnZXN0b2NrLWVycCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNtYWluLWxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJNYWluTGF5b3V0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBOEoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZXN0b2NrLWVycC8/MWZhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk1haW5MYXlvdXRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxpc3NhbVxcXFwudnNjb2RlXFxcXGdlc3RvY2tcXFxcZ2VzdG9jay1lcnBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0XFxcXG1haW4tbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cissam%5C%5C.vscode%5C%5Cgestock%5C%5Cgestock-erp%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cmain-layout.tsx%22%2C%22ids%22%3A%5B%22MainLayout%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/icons */ \"(ssr)/./src/components/ui/icons.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header({ onMenuClick }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onMenuClick,\n                        className: \"lg:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_1__.Menu, {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_1__.Search, {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                placeholder: \"Rechercher...\",\n                                className: \"pl-10 w-64\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_1__.Bell, {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                children: \"3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-900\",\n                                        children: \"John Doe\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Administrateur\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_1__.User, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\header.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MainLayout: () => (/* binding */ MainLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header */ \"(ssr)/./src/components/layout/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ MainLayout auto */ \n\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nfunction MainLayout({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex w-full max-w-xs flex-1 flex-col bg-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        onMenuClick: ()=>setSidebarOpen(true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\main-layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/main-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/icons */ \"(ssr)/./src/components/ui/icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nconst navigation = [\n    {\n        name: \"Tableau de bord\",\n        href: \"/dashboard\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.LayoutDashboard\n    },\n    {\n        name: \"Gestion des stocks\",\n        href: \"/inventory\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.Package,\n        children: [\n            {\n                name: \"Produits\",\n                href: \"/inventory/products\"\n            },\n            {\n                name: \"Cat\\xe9gories\",\n                href: \"/inventory/categories\"\n            },\n            {\n                name: \"Entrep\\xf4ts\",\n                href: \"/inventory/warehouses\"\n            },\n            {\n                name: \"Mouvements\",\n                href: \"/inventory/movements\"\n            }\n        ]\n    },\n    {\n        name: \"Fournisseurs\",\n        href: \"/suppliers\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.UserCheck\n    },\n    {\n        name: \"Clients\",\n        href: \"/customers\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.Users\n    },\n    {\n        name: \"Facturation\",\n        href: \"/invoicing\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.FileText,\n        children: [\n            {\n                name: \"Factures\",\n                href: \"/invoicing/invoices\"\n            },\n            {\n                name: \"Devis\",\n                href: \"/invoicing/quotes\"\n            },\n            {\n                name: \"Avoirs\",\n                href: \"/invoicing/credit-notes\"\n            }\n        ]\n    },\n    {\n        name: \"Achats\",\n        href: \"/purchases\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.ShoppingCart\n    },\n    {\n        name: \"Ventes\",\n        href: \"/sales\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.TrendingUp\n    },\n    {\n        name: \"Rapports\",\n        href: \"/reports\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.BarChart3\n    }\n];\nconst bottomNavigation = [\n    {\n        name: \"Param\\xe8tres\",\n        href: \"/settings\",\n        icon: _components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.Settings\n    }\n];\nfunction Sidebar({ className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: cn(\"flex h-full w-64 flex-col bg-white border-r border-gray-200\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center px-6 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.Warehouse, {\n                            className: \"h-8 w-8 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xl font-bold text-gray-900\",\n                            children: \"GestStock\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-3 py-4\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\");\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: item.href,\n                                className: cn(\"group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors\", isActive ? \"bg-blue-50 text-blue-700 border-r-2 border-blue-600\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: cn(\"mr-3 h-5 w-5 flex-shrink-0\", isActive ? \"text-blue-600\" : \"text-gray-400 group-hover:text-gray-500\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this),\n                            item.children && isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-8 mt-1 space-y-1\",\n                                children: item.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: child.href,\n                                        className: cn(\"block px-3 py-1 text-sm rounded-md transition-colors\", pathname === child.href ? \"text-blue-700 bg-blue-50\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-50\"),\n                                        children: child.name\n                                    }, child.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-3\",\n                children: [\n                    bottomNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: item.href,\n                            className: \"group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                    className: \"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                item.name\n                            ]\n                        }, item.name, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"group flex w-full items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_3__.LogOut, {\n                                className: \"mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            \"D\\xe9connexion\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\n// Button variants\nconst buttonVariants = {\n    variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n        success: \"bg-green-600 text-white hover:bg-green-700\",\n        warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n    },\n    size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    }\n};\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variantClasses = buttonVariants.variant[variant];\n    const sizeClasses = buttonVariants.size[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: cn(baseClasses, variantClasses, sizeClasses, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 40,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUU5Qix5Q0FBeUM7QUFDekMsU0FBU0MsR0FBRyxHQUFHQyxPQUE4QztJQUMzRCxPQUFPQSxRQUFRQyxNQUFNLENBQUNDLFNBQVNDLElBQUksQ0FBQztBQUN0QztBQUVBLGtCQUFrQjtBQUNsQixNQUFNQyxpQkFBaUI7SUFDckJDLFNBQVM7UUFDUEMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxTQUFTO0lBQ1g7SUFDQUMsTUFBTTtRQUNKUixTQUFTO1FBQ1RTLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxNQUFNO0lBQ1I7QUFDRjtBQU9BLE1BQU1DLHVCQUFTcEIsNkNBQWdCLENBQzdCLENBQUMsRUFBRXNCLFNBQVMsRUFBRWYsVUFBVSxTQUFTLEVBQUVTLE9BQU8sU0FBUyxFQUFFLEdBQUdPLE9BQU8sRUFBRUM7SUFDL0QsTUFBTUMsY0FBYztJQUNwQixNQUFNQyxpQkFBaUJwQixlQUFlQyxPQUFPLENBQUNBLFFBQVE7SUFDdEQsTUFBTW9CLGNBQWNyQixlQUFlVSxJQUFJLENBQUNBLEtBQUs7SUFFN0MscUJBQ0UsOERBQUNZO1FBQ0NOLFdBQVdyQixHQUFHd0IsYUFBYUMsZ0JBQWdCQyxhQUFhTDtRQUN4REUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSCxPQUFPUyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2dlc3RvY2stZXJwLy4vc3JjL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeD82YTBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbi8vIFV0aWxpdHkgZnVuY3Rpb24gZm9yIGNvbWJpbmluZyBjbGFzc2VzXG5mdW5jdGlvbiBjbiguLi5jbGFzc2VzOiAoc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbCB8IGZhbHNlKVtdKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNsYXNzZXMuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJyAnKVxufVxuXG4vLyBCdXR0b24gdmFyaWFudHNcbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0ge1xuICB2YXJpYW50OiB7XG4gICAgZGVmYXVsdDogXCJiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLWJsdWUtNzAwXCIsXG4gICAgZGVzdHJ1Y3RpdmU6IFwiYmctcmVkLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC03MDBcIixcbiAgICBvdXRsaW5lOiBcImJvcmRlciBib3JkZXItZ3JheS0zMDAgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCBob3Zlcjp0ZXh0LWdyYXktOTAwXCIsXG4gICAgc2Vjb25kYXJ5OiBcImJnLWdyYXktMTAwIHRleHQtZ3JheS05MDAgaG92ZXI6YmctZ3JheS0yMDBcIixcbiAgICBnaG9zdDogXCJob3ZlcjpiZy1ncmF5LTEwMCBob3Zlcjp0ZXh0LWdyYXktOTAwXCIsXG4gICAgbGluazogXCJ0ZXh0LWJsdWUtNjAwIHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICBzdWNjZXNzOiBcImJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLWdyZWVuLTcwMFwiLFxuICAgIHdhcm5pbmc6IFwiYmcteWVsbG93LTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXllbGxvdy03MDBcIixcbiAgfSxcbiAgc2l6ZToge1xuICAgIGRlZmF1bHQ6IFwiaC0xMCBweC00IHB5LTJcIixcbiAgICBzbTogXCJoLTkgcm91bmRlZC1tZCBweC0zXCIsXG4gICAgbGc6IFwiaC0xMSByb3VuZGVkLW1kIHB4LThcIixcbiAgICBpY29uOiBcImgtMTAgdy0xMFwiLFxuICB9LFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIEJ1dHRvblByb3BzIGV4dGVuZHMgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+IHtcbiAgdmFyaWFudD86IGtleW9mIHR5cGVvZiBidXR0b25WYXJpYW50cy52YXJpYW50XG4gIHNpemU/OiBrZXlvZiB0eXBlb2YgYnV0dG9uVmFyaWFudHMuc2l6ZVxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCA9IFwiZGVmYXVsdFwiLCBzaXplID0gXCJkZWZhdWx0XCIsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIGNvbnN0IGJhc2VDbGFzc2VzID0gXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1ibHVlLTUwMCBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICBjb25zdCB2YXJpYW50Q2xhc3NlcyA9IGJ1dHRvblZhcmlhbnRzLnZhcmlhbnRbdmFyaWFudF1cbiAgICBjb25zdCBzaXplQ2xhc3NlcyA9IGJ1dHRvblZhcmlhbnRzLnNpemVbc2l6ZV1cblxuICAgIHJldHVybiAoXG4gICAgICA8YnV0dG9uXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYmFzZUNsYXNzZXMsIHZhcmlhbnRDbGFzc2VzLCBzaXplQ2xhc3NlcywgY2xhc3NOYW1lKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5CdXR0b24uZGlzcGxheU5hbWUgPSBcIkJ1dHRvblwiXG5cbmV4cG9ydCB7IEJ1dHRvbiB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsImNsYXNzZXMiLCJmaWx0ZXIiLCJCb29sZWFuIiwiam9pbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzdWNjZXNzIiwid2FybmluZyIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImJhc2VDbGFzc2VzIiwidmFyaWFudENsYXNzZXMiLCJzaXplQ2xhc3NlcyIsImJ1dHRvbiIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/icons.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/icons.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertTriangle: () => (/* binding */ AlertTriangle),\n/* harmony export */   BarChart3: () => (/* binding */ BarChart3),\n/* harmony export */   Bell: () => (/* binding */ Bell),\n/* harmony export */   Clock: () => (/* binding */ Clock),\n/* harmony export */   DollarSign: () => (/* binding */ DollarSign),\n/* harmony export */   Edit: () => (/* binding */ Edit),\n/* harmony export */   FileText: () => (/* binding */ FileText),\n/* harmony export */   Filter: () => (/* binding */ Filter),\n/* harmony export */   LayoutDashboard: () => (/* binding */ LayoutDashboard),\n/* harmony export */   LogOut: () => (/* binding */ LogOut),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   Package: () => (/* binding */ Package),\n/* harmony export */   Plus: () => (/* binding */ Plus),\n/* harmony export */   Search: () => (/* binding */ Search),\n/* harmony export */   Settings: () => (/* binding */ Settings),\n/* harmony export */   ShoppingCart: () => (/* binding */ ShoppingCart),\n/* harmony export */   Trash2: () => (/* binding */ Trash2),\n/* harmony export */   TrendingUp: () => (/* binding */ TrendingUp),\n/* harmony export */   User: () => (/* binding */ User),\n/* harmony export */   UserCheck: () => (/* binding */ UserCheck),\n/* harmony export */   Users: () => (/* binding */ Users),\n/* harmony export */   Warehouse: () => (/* binding */ Warehouse)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Simple SVG icons to replace lucide-react temporarily\n\n\nconst LayoutDashboard = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"3\",\n                y: \"3\",\n                width: \"7\",\n                height: \"9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"14\",\n                y: \"3\",\n                width: \"7\",\n                height: \"5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 12,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"14\",\n                y: \"12\",\n                width: \"7\",\n                height: \"9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"3\",\n                y: \"16\",\n                width: \"7\",\n                height: \"5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 14,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\nconst Package = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m7.5 4.27 9 5.15\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m3.3 7 8.7 5 8.7-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 22V12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\nconst Users = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 30,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m22 21-3-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 31,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m16 16 3 3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\nconst UserCheck = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 39,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,11 18,13 22,9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined);\nconst FileText = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 46,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"14,2 14,8 20,8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 47,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"16\",\n                y1: \"13\",\n                x2: \"8\",\n                y2: \"13\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 48,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"16\",\n                y1: \"17\",\n                x2: \"8\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"10,9 9,9 8,9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 50,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\nconst ShoppingCart = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"8\",\n                cy: \"21\",\n                r: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 56,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"19\",\n                cy: \"21\",\n                r: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 57,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 55,\n        columnNumber: 3\n    }, undefined);\nconst TrendingUp = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"22,7 13.5,15.5 8.5,10.5 2,17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 64,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,7 22,7 22,13\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 65,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined);\nconst Settings = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 71,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 72,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined);\nconst LogOut = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 78,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,17 21,12 16,7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 79,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"21\",\n                y1: \"12\",\n                x2: \"9\",\n                y2: \"12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 80,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined);\nconst Warehouse = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 86,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 18h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 87,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 14h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 88,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                width: \"12\",\n                height: \"12\",\n                x: \"6\",\n                y: \"10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 89,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\nconst BarChart3 = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 3v18h18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 95,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18 17V9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 96,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M13 17V5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 97,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 17v-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 98,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined);\nconst Plus = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M5 12h14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 5v14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\nconst Search = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"11\",\n                cy: \"11\",\n                r: \"8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 111,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m21 21-4.35-4.35\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 112,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 110,\n        columnNumber: 3\n    }, undefined);\nconst Filter = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n            points: \"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 118,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 117,\n        columnNumber: 3\n    }, undefined);\nconst Edit = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 124,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 125,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined);\nconst Trash2 = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 6h18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 132,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 133,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"11\",\n                x2: \"10\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 134,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"14\",\n                y1: \"11\",\n                x2: \"14\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 135,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 130,\n        columnNumber: 3\n    }, undefined);\nconst AlertTriangle = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 141,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 9v4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 142,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 17h.01\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 143,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 140,\n        columnNumber: 3\n    }, undefined);\nconst Bell = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 149,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 150,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 148,\n        columnNumber: 3\n    }, undefined);\nconst User = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 156,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 157,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined);\nconst Menu = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"12\",\n                x2: \"20\",\n                y2: \"12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 163,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"6\",\n                x2: \"20\",\n                y2: \"6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 164,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"18\",\n                x2: \"20\",\n                y2: \"18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 165,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 162,\n        columnNumber: 3\n    }, undefined);\nconst DollarSign = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"12\",\n                y1: \"1\",\n                x2: \"12\",\n                y2: \"23\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 171,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 172,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 170,\n        columnNumber: 3\n    }, undefined);\nconst Clock = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 178,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"12,6 12,12 16,14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 179,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 177,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/icons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: cn(\"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"335c2499c28e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VzdG9jay1lcnAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JmMGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzMzVjMjQ5OWMyOGVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/invoicing/credit-notes/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/invoicing/credit-notes/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreditNotesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(rsc)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(rsc)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/icons */ \"(rsc)/./src/components/ui/icons.tsx\");\n\n\n\n\n\n\nconst creditNotes = [\n    {\n        id: \"1\",\n        number: \"AVO-2024-001\",\n        customer: \"Entreprise ABC\",\n        originalInvoice: \"FAC-2024-001\",\n        date: \"2024-01-16\",\n        amount: 125.00,\n        reason: \"Produit d\\xe9fectueux\",\n        status: \"Valid\\xe9\"\n    },\n    {\n        id: \"2\",\n        number: \"AVO-2024-002\",\n        customer: \"Soci\\xe9t\\xe9 XYZ\",\n        originalInvoice: \"FAC-2024-015\",\n        date: \"2024-01-14\",\n        amount: 89.50,\n        reason: \"Erreur de facturation\",\n        status: \"En attente\"\n    },\n    {\n        id: \"3\",\n        number: \"AVO-2024-003\",\n        customer: \"SARL Martin\",\n        originalInvoice: \"FAC-2024-023\",\n        date: \"2024-01-12\",\n        amount: 210.00,\n        reason: \"Retour marchandise\",\n        status: \"Valid\\xe9\"\n    }\n];\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"Valid\\xe9\":\n            return \"bg-green-100 text-green-800\";\n        case \"En attente\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"Annul\\xe9\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\nfunction CreditNotesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_1__.MainLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900\",\n                                    children: \"Avoirs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"G\\xe9rez les notes de cr\\xe9dit et remboursements\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                \"Nouvel avoir\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Search, {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            placeholder: \"Rechercher par num\\xe9ro ou client...\",\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Filter, {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Filtres\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.FileText, {\n                                            className: \"h-8 w-8 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total avoirs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: creditNotes.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.DollarSign, {\n                                            className: \"h-8 w-8 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Montant total\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: [\n                                                        \"-€\",\n                                                        creditNotes.reduce((acc, note)=>acc + note.amount, 0).toFixed(2)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.FileText, {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Valid\\xe9s\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: creditNotes.filter((note)=>note.status === \"Valid\\xe9\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Liste des Avoirs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Num\\xe9ro\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Facture origine\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-right py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Montant\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Motif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Statut\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center py-3 px-4 font-medium text-gray-600\",\n                                                        children: \"Actions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: creditNotes.map((note)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-100 hover:bg-gray-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: note.number\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-gray-900\",\n                                                            children: note.customer\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-blue-600 hover:text-blue-800 cursor-pointer\",\n                                                            children: note.originalInvoice\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-gray-600\",\n                                                            children: new Date(note.date).toLocaleDateString(\"fr-FR\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-right text-red-600 font-medium\",\n                                                            children: [\n                                                                \"-€\",\n                                                                note.amount.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-gray-600\",\n                                                            children: note.reason\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(note.status)}`,\n                                                                children: note.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Edit, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_icons__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, note.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\invoicing\\\\credit-notes\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/invoicing/credit-notes/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"GestStock ERP - Gestion d'entreprise pour PME\",\n    description: \"Solution ERP compl\\xe8te pour la gestion des stocks, clients, fournisseurs, facturation et ventes\",\n    keywords: [\n        \"ERP\",\n        \"gestion\",\n        \"stock\",\n        \"facturation\",\n        \"PME\",\n        \"CRM\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-gray-50 text-gray-900\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFPaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBTztRQUFXO1FBQVM7UUFBZTtRQUFPO0tBQU07QUFDcEUsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLFdBQVdULGtMQUFjO2tCQUN2Qyw0RUFBQ1c7WUFBS0YsV0FBVTtzQkFDYkg7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZXN0b2NrLWVycC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1pbnRlclwiLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkdlc3RTdG9jayBFUlAgLSBHZXN0aW9uIGQnZW50cmVwcmlzZSBwb3VyIFBNRVwiLFxuICBkZXNjcmlwdGlvbjogXCJTb2x1dGlvbiBFUlAgY29tcGzDqHRlIHBvdXIgbGEgZ2VzdGlvbiBkZXMgc3RvY2tzLCBjbGllbnRzLCBmb3Vybmlzc2V1cnMsIGZhY3R1cmF0aW9uIGV0IHZlbnRlc1wiLFxuICBrZXl3b3JkczogW1wiRVJQXCIsIFwiZ2VzdGlvblwiLCBcInN0b2NrXCIsIFwiZmFjdHVyYXRpb25cIiwgXCJQTUVcIiwgXCJDUk1cIl0sXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJmclwiIGNsYXNzTmFtZT17aW50ZXIudmFyaWFibGV9PlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zIGFudGlhbGlhc2VkIGJnLWdyYXktNTAgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/main-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/main-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MainLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\.vscode\gestock\gestock-erp\src\components\layout\main-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\.vscode\gestock\gestock-erp\src\components\layout\main-layout.tsx#MainLayout`);


/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\n// Button variants\nconst buttonVariants = {\n    variant: {\n        default: \"bg-blue-600 text-white hover:bg-blue-700\",\n        destructive: \"bg-red-600 text-white hover:bg-red-700\",\n        outline: \"border border-gray-300 bg-white hover:bg-gray-50 hover:text-gray-900\",\n        secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200\",\n        ghost: \"hover:bg-gray-100 hover:text-gray-900\",\n        link: \"text-blue-600 underline-offset-4 hover:underline\",\n        success: \"bg-green-600 text-white hover:bg-green-700\",\n        warning: \"bg-yellow-600 text-white hover:bg-yellow-700\"\n    },\n    size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\"\n    }\n};\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant = \"default\", size = \"default\", ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    const variantClasses = buttonVariants.variant[variant];\n    const sizeClasses = buttonVariants.size[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: cn(baseClasses, variantClasses, sizeClasses, className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 40,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: cn(\"rounded-lg border border-gray-200 bg-white text-gray-950 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: cn(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: cn(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: cn(\"text-sm text-gray-500\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: cn(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: cn(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUU5Qix5Q0FBeUM7QUFDekMsU0FBU0MsR0FBRyxHQUFHQyxPQUE4QztJQUMzRCxPQUFPQSxRQUFRQyxNQUFNLENBQUNDLFNBQVNDLElBQUksQ0FBQztBQUN0QztBQUVBLE1BQU1DLHFCQUFPTiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFUSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdQLEdBQ1Qsc0VBQ0FPO1FBRUQsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILEtBQUtNLFdBQVcsR0FBRztBQUVuQixNQUFNQywyQkFBYWIsNkNBQWdCLENBR2pDLENBQUMsRUFBRVEsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXUCxHQUFHLGlDQUFpQ087UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWWQsNkNBQWdCLENBR2hDLENBQUMsRUFBRVEsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXUCxHQUNULHNEQUNBTztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCaEIsNkNBQWdCLENBR3RDLENBQUMsRUFBRVEsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXUCxHQUFHLHlCQUF5Qk87UUFDdEMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JPLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjbEIsNkNBQWdCLENBR2xDLENBQUMsRUFBRVEsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXUCxHQUFHLFlBQVlPO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFuQiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFUSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdQLEdBQUcsOEJBQThCTztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VzdG9jay1lcnAvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeD9lN2QyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbi8vIFV0aWxpdHkgZnVuY3Rpb24gZm9yIGNvbWJpbmluZyBjbGFzc2VzXG5mdW5jdGlvbiBjbiguLi5jbGFzc2VzOiAoc3RyaW5nIHwgdW5kZWZpbmVkIHwgbnVsbCB8IGZhbHNlKVtdKTogc3RyaW5nIHtcbiAgcmV0dXJuIGNsYXNzZXMuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJyAnKVxufVxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLXdoaXRlIHRleHQtZ3JheS05NTAgc2hhZG93LXNtXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkLmRpc3BsYXlOYW1lID0gXCJDYXJkXCJcblxuY29uc3QgQ2FyZEhlYWRlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMS41IHAtNlwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkSGVhZGVyXCJcblxuY29uc3QgQ2FyZFRpdGxlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxIZWFkaW5nRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGgzXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBsZWFkaW5nLW5vbmUgdHJhY2tpbmctdGlnaHRcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9IFwiQ2FyZFRpdGxlXCJcblxuY29uc3QgQ2FyZERlc2NyaXB0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxQYXJhZ3JhcGhFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8cFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZERlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gXCJDYXJkRGVzY3JpcHRpb25cIlxuXG5jb25zdCBDYXJkQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9IFwiQ2FyZENvbnRlbnRcIlxuXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRGb290ZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRGb290ZXJcIlxuXG5leHBvcnQgeyBDYXJkLCBDYXJkSGVhZGVyLCBDYXJkRm9vdGVyLCBDYXJkVGl0bGUsIENhcmREZXNjcmlwdGlvbiwgQ2FyZENvbnRlbnQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJjbGFzc2VzIiwiZmlsdGVyIiwiQm9vbGVhbiIsImpvaW4iLCJDYXJkIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiZGl2IiwiZGlzcGxheU5hbWUiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiaDMiLCJDYXJkRGVzY3JpcHRpb24iLCJwIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/icons.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/icons.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertTriangle: () => (/* binding */ AlertTriangle),\n/* harmony export */   BarChart3: () => (/* binding */ BarChart3),\n/* harmony export */   Bell: () => (/* binding */ Bell),\n/* harmony export */   Clock: () => (/* binding */ Clock),\n/* harmony export */   DollarSign: () => (/* binding */ DollarSign),\n/* harmony export */   Edit: () => (/* binding */ Edit),\n/* harmony export */   FileText: () => (/* binding */ FileText),\n/* harmony export */   Filter: () => (/* binding */ Filter),\n/* harmony export */   LayoutDashboard: () => (/* binding */ LayoutDashboard),\n/* harmony export */   LogOut: () => (/* binding */ LogOut),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   Package: () => (/* binding */ Package),\n/* harmony export */   Plus: () => (/* binding */ Plus),\n/* harmony export */   Search: () => (/* binding */ Search),\n/* harmony export */   Settings: () => (/* binding */ Settings),\n/* harmony export */   ShoppingCart: () => (/* binding */ ShoppingCart),\n/* harmony export */   Trash2: () => (/* binding */ Trash2),\n/* harmony export */   TrendingUp: () => (/* binding */ TrendingUp),\n/* harmony export */   User: () => (/* binding */ User),\n/* harmony export */   UserCheck: () => (/* binding */ UserCheck),\n/* harmony export */   Users: () => (/* binding */ Users),\n/* harmony export */   Warehouse: () => (/* binding */ Warehouse)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Simple SVG icons to replace lucide-react temporarily\n\n\nconst LayoutDashboard = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"3\",\n                y: \"3\",\n                width: \"7\",\n                height: \"9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 11,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"14\",\n                y: \"3\",\n                width: \"7\",\n                height: \"5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 12,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"14\",\n                y: \"12\",\n                width: \"7\",\n                height: \"9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                x: \"3\",\n                y: \"16\",\n                width: \"7\",\n                height: \"5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 14,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\nconst Package = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m7.5 4.27 9 5.15\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 21,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m3.3 7 8.7 5 8.7-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 22,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 22V12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 19,\n        columnNumber: 3\n    }, undefined);\nconst Users = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 29,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 30,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m22 21-3-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 31,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m16 16 3 3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 32,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\nconst UserCheck = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"9\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 39,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,11 18,13 22,9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 40,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined);\nconst FileText = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 46,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"14,2 14,8 20,8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 47,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"16\",\n                y1: \"13\",\n                x2: \"8\",\n                y2: \"13\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 48,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"16\",\n                y1: \"17\",\n                x2: \"8\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 49,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"10,9 9,9 8,9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 50,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\nconst ShoppingCart = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"8\",\n                cy: \"21\",\n                r: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 56,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"19\",\n                cy: \"21\",\n                r: \"1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 57,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 55,\n        columnNumber: 3\n    }, undefined);\nconst TrendingUp = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"22,7 13.5,15.5 8.5,10.5 2,17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 64,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,7 22,7 22,13\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 65,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined);\nconst Settings = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 71,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 72,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined);\nconst LogOut = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 78,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"16,17 21,12 16,7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 79,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"21\",\n                y1: \"12\",\n                x2: \"9\",\n                y2: \"12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 80,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 77,\n        columnNumber: 3\n    }, undefined);\nconst Warehouse = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M22 8.35V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V8.35A2 2 0 0 1 3.26 6.5l8-3.2a2 2 0 0 1 1.48 0l8 3.2A2 2 0 0 1 22 8.35Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 86,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 18h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 87,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 14h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 88,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                width: \"12\",\n                height: \"12\",\n                x: \"6\",\n                y: \"10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 89,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined);\nconst BarChart3 = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 3v18h18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 95,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18 17V9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 96,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M13 17V5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 97,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 17v-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 98,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 94,\n        columnNumber: 3\n    }, undefined);\nconst Plus = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M5 12h14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 5v14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined);\nconst Search = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"11\",\n                cy: \"11\",\n                r: \"8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 111,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m21 21-4.35-4.35\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 112,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 110,\n        columnNumber: 3\n    }, undefined);\nconst Filter = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n            points: \"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n            lineNumber: 118,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 117,\n        columnNumber: 3\n    }, undefined);\nconst Edit = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 124,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 125,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined);\nconst Trash2 = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 6h18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 132,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 133,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"10\",\n                y1: \"11\",\n                x2: \"10\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 134,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"14\",\n                y1: \"11\",\n                x2: \"14\",\n                y2: \"17\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 135,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 130,\n        columnNumber: 3\n    }, undefined);\nconst AlertTriangle = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 141,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 9v4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 142,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 17h.01\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 143,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 140,\n        columnNumber: 3\n    }, undefined);\nconst Bell = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 149,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 150,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 148,\n        columnNumber: 3\n    }, undefined);\nconst User = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 156,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"7\",\n                r: \"4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 157,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 155,\n        columnNumber: 3\n    }, undefined);\nconst Menu = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"12\",\n                x2: \"20\",\n                y2: \"12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 163,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"6\",\n                x2: \"20\",\n                y2: \"6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 164,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"4\",\n                y1: \"18\",\n                x2: \"20\",\n                y2: \"18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 165,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 162,\n        columnNumber: 3\n    }, undefined);\nconst DollarSign = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"12\",\n                y1: \"1\",\n                x2: \"12\",\n                y2: \"23\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 171,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 172,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 170,\n        columnNumber: 3\n    }, undefined);\nconst Clock = ({ className, size = 20 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        width: size,\n        height: size,\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 178,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                points: \"12,6 12,12 16,14\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n                lineNumber: 179,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\icons.tsx\",\n        lineNumber: 177,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9pY29ucy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx1REFBdUQ7O0FBQzlCO0FBT2xCLE1BQU1DLGtCQUFrQixDQUFDLEVBQUVDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQ2pFLDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNDO2dCQUFLQyxHQUFFO2dCQUFJQyxHQUFFO2dCQUFJUixPQUFNO2dCQUFJQyxRQUFPOzs7Ozs7MEJBQ25DLDhEQUFDSztnQkFBS0MsR0FBRTtnQkFBS0MsR0FBRTtnQkFBSVIsT0FBTTtnQkFBSUMsUUFBTzs7Ozs7OzBCQUNwQyw4REFBQ0s7Z0JBQUtDLEdBQUU7Z0JBQUtDLEdBQUU7Z0JBQUtSLE9BQU07Z0JBQUlDLFFBQU87Ozs7OzswQkFDckMsOERBQUNLO2dCQUFLQyxHQUFFO2dCQUFJQyxHQUFFO2dCQUFLUixPQUFNO2dCQUFJQyxRQUFPOzs7Ozs7Ozs7OztrQkFFdkM7QUFFTSxNQUFNUSxVQUFVLENBQUMsRUFBRVosU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDekQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTUMsUUFBUSxDQUFDLEVBQUVmLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQ3ZELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNLO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQ1IsOERBQUNFO2dCQUFPQyxJQUFHO2dCQUFJQyxJQUFHO2dCQUFJQyxHQUFFOzs7Ozs7MEJBQ3hCLDhEQUFDTjtnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDRDtnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7a0JBRVg7QUFFTSxNQUFNTSxZQUFZLENBQUMsRUFBRXBCLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQzNELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNLO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQ1IsOERBQUNFO2dCQUFPQyxJQUFHO2dCQUFJQyxJQUFHO2dCQUFJQyxHQUFFOzs7Ozs7MEJBQ3hCLDhEQUFDRTtnQkFBU0MsUUFBTzs7Ozs7Ozs7Ozs7a0JBRXBCO0FBRU0sTUFBTUMsV0FBVyxDQUFDLEVBQUV2QixTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUMxRCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDSztnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDTztnQkFBU0MsUUFBTzs7Ozs7OzBCQUNqQiw4REFBQ0U7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUlDLElBQUc7Ozs7OzswQkFDaEMsOERBQUNKO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFJQyxJQUFHOzs7Ozs7MEJBQ2hDLDhEQUFDUDtnQkFBU0MsUUFBTzs7Ozs7Ozs7Ozs7a0JBRXBCO0FBRU0sTUFBTU8sZUFBZSxDQUFDLEVBQUU3QixTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUM5RCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDUTtnQkFBT0MsSUFBRztnQkFBSUMsSUFBRztnQkFBS0MsR0FBRTs7Ozs7OzBCQUN6Qiw4REFBQ0g7Z0JBQU9DLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDMUIsOERBQUNOO2dCQUFLQyxHQUFFOzs7Ozs7Ozs7OztrQkFFWDtBQUVNLE1BQU1nQixhQUFhLENBQUMsRUFBRTlCLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQzVELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNhO2dCQUFTQyxRQUFPOzs7Ozs7MEJBQ2pCLDhEQUFDRDtnQkFBU0MsUUFBTzs7Ozs7Ozs7Ozs7a0JBRXBCO0FBRU0sTUFBTVMsV0FBVyxDQUFDLEVBQUUvQixTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUMxRCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDUTtnQkFBT0MsSUFBRztnQkFBS0MsSUFBRztnQkFBS0MsR0FBRTs7Ozs7OzBCQUMxQiw4REFBQ047Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTWtCLFNBQVMsQ0FBQyxFQUFFaEMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDeEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ087Z0JBQVNDLFFBQU87Ozs7OzswQkFDakIsOERBQUNFO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFJQyxJQUFHOzs7Ozs7Ozs7OztrQkFFbkM7QUFFTSxNQUFNSyxZQUFZLENBQUMsRUFBRWpDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQzNELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNLO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQ1IsOERBQUNEO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQ1IsOERBQUNEO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQ1IsOERBQUNMO2dCQUFLTixPQUFNO2dCQUFLQyxRQUFPO2dCQUFLTSxHQUFFO2dCQUFJQyxHQUFFOzs7Ozs7Ozs7OztrQkFFeEM7QUFFTSxNQUFNdUIsWUFBWSxDQUFDLEVBQUVsQyxTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUMzRCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDSztnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDRDtnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDRDtnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDRDtnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7a0JBRVg7QUFFTSxNQUFNcUIsT0FBTyxDQUFDLEVBQUVuQyxTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUN0RCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDSztnQkFBS0MsR0FBRTs7Ozs7OzBCQUNSLDhEQUFDRDtnQkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7a0JBRVg7QUFFTSxNQUFNc0IsU0FBUyxDQUFDLEVBQUVwQyxTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUN4RCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDUTtnQkFBT0MsSUFBRztnQkFBS0MsSUFBRztnQkFBS0MsR0FBRTs7Ozs7OzBCQUMxQiw4REFBQ047Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTXVCLFNBQVMsQ0FBQyxFQUFFckMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDeEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7a0JBQ3RILDRFQUFDOEI7WUFBUWhCLFFBQU87Ozs7Ozs7Ozs7a0JBRW5CO0FBRU0sTUFBTWlCLE9BQU8sQ0FBQyxFQUFFdkMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDdEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTTBCLFNBQVMsQ0FBQyxFQUFFeEMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDeEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ1U7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Ozs7OzswQkFDakMsOERBQUNKO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHOzs7Ozs7Ozs7OztrQkFFcEM7QUFFTSxNQUFNYSxnQkFBZ0IsQ0FBQyxFQUFFekMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDL0QsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTTRCLE9BQU8sQ0FBQyxFQUFFMUMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDdEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0Q7Z0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUVYO0FBRU0sTUFBTTZCLE9BQU8sQ0FBQyxFQUFFM0MsU0FBUyxFQUFFQyxPQUFPLEVBQUUsRUFBYSxpQkFDdEQsOERBQUNDO1FBQUlGLFdBQVdBO1FBQVdHLE9BQU9GO1FBQU1HLFFBQVFIO1FBQU1JLFNBQVE7UUFBWUMsTUFBSztRQUFPQyxRQUFPO1FBQWVDLGFBQVk7OzBCQUN0SCw4REFBQ0s7Z0JBQUtDLEdBQUU7Ozs7OzswQkFDUiw4REFBQ0U7Z0JBQU9DLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUlDLEdBQUU7Ozs7Ozs7Ozs7O2tCQUU1QjtBQUVNLE1BQU15QixPQUFPLENBQUMsRUFBRTVDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQ3RELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNnQjtnQkFBS0MsSUFBRztnQkFBSUMsSUFBRztnQkFBS0MsSUFBRztnQkFBS0MsSUFBRzs7Ozs7OzBCQUNoQyw4REFBQ0o7Z0JBQUtDLElBQUc7Z0JBQUlDLElBQUc7Z0JBQUlDLElBQUc7Z0JBQUtDLElBQUc7Ozs7OzswQkFDL0IsOERBQUNKO2dCQUFLQyxJQUFHO2dCQUFJQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxJQUFHOzs7Ozs7Ozs7OztrQkFFbkM7QUFFTSxNQUFNaUIsYUFBYSxDQUFDLEVBQUU3QyxTQUFTLEVBQUVDLE9BQU8sRUFBRSxFQUFhLGlCQUM1RCw4REFBQ0M7UUFBSUYsV0FBV0E7UUFBV0csT0FBT0Y7UUFBTUcsUUFBUUg7UUFBTUksU0FBUTtRQUFZQyxNQUFLO1FBQU9DLFFBQU87UUFBZUMsYUFBWTs7MEJBQ3RILDhEQUFDZ0I7Z0JBQUtDLElBQUc7Z0JBQUtDLElBQUc7Z0JBQUlDLElBQUc7Z0JBQUtDLElBQUc7Ozs7OzswQkFDaEMsOERBQUNmO2dCQUFLQyxHQUFFOzs7Ozs7Ozs7OztrQkFFWDtBQUVNLE1BQU1nQyxRQUFRLENBQUMsRUFBRTlDLFNBQVMsRUFBRUMsT0FBTyxFQUFFLEVBQWEsaUJBQ3ZELDhEQUFDQztRQUFJRixXQUFXQTtRQUFXRyxPQUFPRjtRQUFNRyxRQUFRSDtRQUFNSSxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsUUFBTztRQUFlQyxhQUFZOzswQkFDdEgsOERBQUNRO2dCQUFPQyxJQUFHO2dCQUFLQyxJQUFHO2dCQUFLQyxHQUFFOzs7Ozs7MEJBQzFCLDhEQUFDRTtnQkFBU0MsUUFBTzs7Ozs7Ozs7Ozs7a0JBRXBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZ2VzdG9jay1lcnAvLi9zcmMvY29tcG9uZW50cy91aS9pY29ucy50c3g/YjA2YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTaW1wbGUgU1ZHIGljb25zIHRvIHJlcGxhY2UgbHVjaWRlLXJlYWN0IHRlbXBvcmFyaWx5XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmludGVyZmFjZSBJY29uUHJvcHMge1xuICBjbGFzc05hbWU/OiBzdHJpbmdcbiAgc2l6ZT86IG51bWJlclxufVxuXG5leHBvcnQgY29uc3QgTGF5b3V0RGFzaGJvYXJkID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxyZWN0IHg9XCIzXCIgeT1cIjNcIiB3aWR0aD1cIjdcIiBoZWlnaHQ9XCI5XCIvPlxuICAgIDxyZWN0IHg9XCIxNFwiIHk9XCIzXCIgd2lkdGg9XCI3XCIgaGVpZ2h0PVwiNVwiLz5cbiAgICA8cmVjdCB4PVwiMTRcIiB5PVwiMTJcIiB3aWR0aD1cIjdcIiBoZWlnaHQ9XCI5XCIvPlxuICAgIDxyZWN0IHg9XCIzXCIgeT1cIjE2XCIgd2lkdGg9XCI3XCIgaGVpZ2h0PVwiNVwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBQYWNrYWdlID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxwYXRoIGQ9XCJtNy41IDQuMjcgOSA1LjE1XCIvPlxuICAgIDxwYXRoIGQ9XCJNMjEgOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczbDcgNGEyIDIgMCAwIDAgMiAwbDctNEEyIDIgMCAwIDAgMjEgMTZaXCIvPlxuICAgIDxwYXRoIGQ9XCJtMy4zIDcgOC43IDUgOC43LTVcIi8+XG4gICAgPHBhdGggZD1cIk0xMiAyMlYxMlwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBVc2VycyA9ICh7IGNsYXNzTmFtZSwgc2l6ZSA9IDIwIH06IEljb25Qcm9wcykgPT4gKFxuICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB3aWR0aD17c2l6ZX0gaGVpZ2h0PXtzaXplfSB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIj5cbiAgICA8cGF0aCBkPVwiTTE2IDIxdi0yYTQgNCAwIDAgMC00LTRINmE0IDQgMCAwIDAtNCA0djJcIi8+XG4gICAgPGNpcmNsZSBjeD1cIjlcIiBjeT1cIjdcIiByPVwiNFwiLz5cbiAgICA8cGF0aCBkPVwibTIyIDIxLTMtM1wiLz5cbiAgICA8cGF0aCBkPVwibTE2IDE2IDMgM1wiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBVc2VyQ2hlY2sgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPHBhdGggZD1cIk0xNiAyMXYtMmE0IDQgMCAwIDAtNC00SDZhNCA0IDAgMCAwLTQgNHYyXCIvPlxuICAgIDxjaXJjbGUgY3g9XCI5XCIgY3k9XCI3XCIgcj1cIjRcIi8+XG4gICAgPHBvbHlsaW5lIHBvaW50cz1cIjE2LDExIDE4LDEzIDIyLDlcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgRmlsZVRleHQgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPHBhdGggZD1cIk0xNCAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWOHpcIi8+XG4gICAgPHBvbHlsaW5lIHBvaW50cz1cIjE0LDIgMTQsOCAyMCw4XCIvPlxuICAgIDxsaW5lIHgxPVwiMTZcIiB5MT1cIjEzXCIgeDI9XCI4XCIgeTI9XCIxM1wiLz5cbiAgICA8bGluZSB4MT1cIjE2XCIgeTE9XCIxN1wiIHgyPVwiOFwiIHkyPVwiMTdcIi8+XG4gICAgPHBvbHlsaW5lIHBvaW50cz1cIjEwLDkgOSw5IDgsOVwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBTaG9wcGluZ0NhcnQgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPGNpcmNsZSBjeD1cIjhcIiBjeT1cIjIxXCIgcj1cIjFcIi8+XG4gICAgPGNpcmNsZSBjeD1cIjE5XCIgY3k9XCIyMVwiIHI9XCIxXCIvPlxuICAgIDxwYXRoIGQ9XCJNMi4wNSAyLjA1aDJsMi42NiAxMi40MmEyIDIgMCAwIDAgMiAxLjU4aDkuNzhhMiAyIDAgMCAwIDEuOTUtMS41N2wxLjY1LTcuNDNINS4xMlwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBUcmVuZGluZ1VwID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxwb2x5bGluZSBwb2ludHM9XCIyMiw3IDEzLjUsMTUuNSA4LjUsMTAuNSAyLDE3XCIvPlxuICAgIDxwb2x5bGluZSBwb2ludHM9XCIxNiw3IDIyLDcgMjIsMTNcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgU2V0dGluZ3MgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPGNpcmNsZSBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIzXCIvPlxuICAgIDxwYXRoIGQ9XCJNMTkuNCAxNWExLjY1IDEuNjUgMCAwIDAgLjMzIDEuODJsLjA2LjA2YTIgMiAwIDAgMSAwIDIuODMgMiAyIDAgMCAxLTIuODMgMGwtLjA2LS4wNmExLjY1IDEuNjUgMCAwIDAtMS44Mi0uMzMgMS42NSAxLjY1IDAgMCAwLTEgMS41MVYyMWEyIDIgMCAwIDEtMiAyIDIgMiAwIDAgMS0yLTJ2LS4wOUExLjY1IDEuNjUgMCAwIDAgOSAxOS40YTEuNjUgMS42NSAwIDAgMC0xLjgyLjMzbC0uMDYuMDZhMiAyIDAgMCAxLTIuODMgMCAyIDIgMCAwIDEgMC0yLjgzbC4wNi0uMDZhMS42NSAxLjY1IDAgMCAwIC4zMy0xLjgyIDEuNjUgMS42NSAwIDAgMC0xLjUxLTFIM2EyIDIgMCAwIDEtMi0yIDIgMiAwIDAgMSAyLTJoLjA5QTEuNjUgMS42NSAwIDAgMCA0LjYgOWExLjY1IDEuNjUgMCAwIDAtLjMzLTEuODJsLS4wNi0uMDZhMiAyIDAgMCAxIDAtMi44MyAyIDIgMCAwIDEgMi44MyAwbC4wNi4wNmExLjY1IDEuNjUgMCAwIDAgMS44Mi4zM0g5YTEuNjUgMS42NSAwIDAgMCAxIDEuNTFWM2EyIDIgMCAwIDEgMi0yIDIgMiAwIDAgMSAyIDJ2LjA5YTEuNjUgMS42NSAwIDAgMCAxIDEuNTEgMS42NSAxLjY1IDAgMCAwIDEuODItLjMzbC4wNi0uMDZhMiAyIDAgMCAxIDIuODMgMCAyIDIgMCAwIDEgMCAyLjgzbC0uMDYuMDZhMS42NSAxLjY1IDAgMCAwLS4zMyAxLjgyVjlhMS42NSAxLjY1IDAgMCAwIDEuNTEgMUgyMWEyIDIgMCAwIDEgMiAyIDIgMiAwIDAgMS0yIDJoLS4wOWExLjY1IDEuNjUgMCAwIDAtMS41MSAxelwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBMb2dPdXQgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPHBhdGggZD1cIk05IDIxSDVhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoNFwiLz5cbiAgICA8cG9seWxpbmUgcG9pbnRzPVwiMTYsMTcgMjEsMTIgMTYsN1wiLz5cbiAgICA8bGluZSB4MT1cIjIxXCIgeTE9XCIxMlwiIHgyPVwiOVwiIHkyPVwiMTJcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgV2FyZWhvdXNlID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxwYXRoIGQ9XCJNMjIgOC4zNVYyMGEyIDIgMCAwIDEtMiAySDRhMiAyIDAgMCAxLTItMlY4LjM1QTIgMiAwIDAgMSAzLjI2IDYuNWw4LTMuMmEyIDIgMCAwIDEgMS40OCAwbDggMy4yQTIgMiAwIDAgMSAyMiA4LjM1WlwiLz5cbiAgICA8cGF0aCBkPVwiTTYgMThoMTJcIi8+XG4gICAgPHBhdGggZD1cIk02IDE0aDEyXCIvPlxuICAgIDxyZWN0IHdpZHRoPVwiMTJcIiBoZWlnaHQ9XCIxMlwiIHg9XCI2XCIgeT1cIjEwXCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IEJhckNoYXJ0MyA9ICh7IGNsYXNzTmFtZSwgc2l6ZSA9IDIwIH06IEljb25Qcm9wcykgPT4gKFxuICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB3aWR0aD17c2l6ZX0gaGVpZ2h0PXtzaXplfSB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIj5cbiAgICA8cGF0aCBkPVwiTTMgM3YxOGgxOFwiLz5cbiAgICA8cGF0aCBkPVwiTTE4IDE3VjlcIi8+XG4gICAgPHBhdGggZD1cIk0xMyAxN1Y1XCIvPlxuICAgIDxwYXRoIGQ9XCJNOCAxN3YtM1wiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBQbHVzID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxwYXRoIGQ9XCJNNSAxMmgxNFwiLz5cbiAgICA8cGF0aCBkPVwiTTEyIDV2MTRcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgU2VhcmNoID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxjaXJjbGUgY3g9XCIxMVwiIGN5PVwiMTFcIiByPVwiOFwiLz5cbiAgICA8cGF0aCBkPVwibTIxIDIxLTQuMzUtNC4zNVwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBGaWx0ZXIgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPHBvbHlnb24gcG9pbnRzPVwiMjIsMyAyLDMgMTAsMTIuNDYgMTAsMTkgMTQsMjEgMTQsMTIuNDZcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgRWRpdCA9ICh7IGNsYXNzTmFtZSwgc2l6ZSA9IDIwIH06IEljb25Qcm9wcykgPT4gKFxuICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB3aWR0aD17c2l6ZX0gaGVpZ2h0PXtzaXplfSB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIj5cbiAgICA8cGF0aCBkPVwiTTExIDRINGEyIDIgMCAwIDAtMiAydjE0YTIgMiAwIDAgMCAyIDJoMTRhMiAyIDAgMCAwIDItMnYtN1wiLz5cbiAgICA8cGF0aCBkPVwiTTE4LjUgMi41YTIuMTIgMi4xMiAwIDAgMSAzIDNMMTIgMTVsLTQgMSAxLTRaXCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IFRyYXNoMiA9ICh7IGNsYXNzTmFtZSwgc2l6ZSA9IDIwIH06IEljb25Qcm9wcykgPT4gKFxuICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB3aWR0aD17c2l6ZX0gaGVpZ2h0PXtzaXplfSB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIj5cbiAgICA8cGF0aCBkPVwiTTMgNmgxOFwiLz5cbiAgICA8cGF0aCBkPVwiTTE5IDZ2MTRjMCAxLTEgMi0yIDJIN2MtMSAwLTItMS0yLTJWNlwiLz5cbiAgICA8cGF0aCBkPVwiTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MlwiLz5cbiAgICA8bGluZSB4MT1cIjEwXCIgeTE9XCIxMVwiIHgyPVwiMTBcIiB5Mj1cIjE3XCIvPlxuICAgIDxsaW5lIHgxPVwiMTRcIiB5MT1cIjExXCIgeDI9XCIxNFwiIHkyPVwiMTdcIi8+XG4gIDwvc3ZnPlxuKVxuXG5leHBvcnQgY29uc3QgQWxlcnRUcmlhbmdsZSA9ICh7IGNsYXNzTmFtZSwgc2l6ZSA9IDIwIH06IEljb25Qcm9wcykgPT4gKFxuICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSB3aWR0aD17c2l6ZX0gaGVpZ2h0PXtzaXplfSB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjJcIj5cbiAgICA8cGF0aCBkPVwibTIxLjczIDE4LTgtMTRhMiAyIDAgMCAwLTMuNDggMGwtOCAxNEEyIDIgMCAwIDAgNCAyMWgxNmEyIDIgMCAwIDAgMS43My0zWlwiLz5cbiAgICA8cGF0aCBkPVwiTTEyIDl2NFwiLz5cbiAgICA8cGF0aCBkPVwiTTEyIDE3aC4wMVwiLz5cbiAgPC9zdmc+XG4pXG5cbmV4cG9ydCBjb25zdCBCZWxsID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxwYXRoIGQ9XCJNNiA4YTYgNiAwIDAgMSAxMiAwYzAgNyAzIDkgMyA5SDNzMy0yIDMtOVwiLz5cbiAgICA8cGF0aCBkPVwiTTEwLjMgMjFhMS45NCAxLjk0IDAgMCAwIDMuNCAwXCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IFVzZXIgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPHBhdGggZD1cIk0yMCAyMXYtMmE0IDQgMCAwIDAtNC00SDhhNCA0IDAgMCAwLTQgNHYyXCIvPlxuICAgIDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiN1wiIHI9XCI0XCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IE1lbnUgPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPGxpbmUgeDE9XCI0XCIgeTE9XCIxMlwiIHgyPVwiMjBcIiB5Mj1cIjEyXCIvPlxuICAgIDxsaW5lIHgxPVwiNFwiIHkxPVwiNlwiIHgyPVwiMjBcIiB5Mj1cIjZcIi8+XG4gICAgPGxpbmUgeDE9XCI0XCIgeTE9XCIxOFwiIHgyPVwiMjBcIiB5Mj1cIjE4XCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IERvbGxhclNpZ24gPSAoeyBjbGFzc05hbWUsIHNpemUgPSAyMCB9OiBJY29uUHJvcHMpID0+IChcbiAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gd2lkdGg9e3NpemV9IGhlaWdodD17c2l6ZX0gdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCIyXCI+XG4gICAgPGxpbmUgeDE9XCIxMlwiIHkxPVwiMVwiIHgyPVwiMTJcIiB5Mj1cIjIzXCIvPlxuICAgIDxwYXRoIGQ9XCJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2XCIvPlxuICA8L3N2Zz5cbilcblxuZXhwb3J0IGNvbnN0IENsb2NrID0gKHsgY2xhc3NOYW1lLCBzaXplID0gMjAgfTogSWNvblByb3BzKSA9PiAoXG4gIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHdpZHRoPXtzaXplfSBoZWlnaHQ9e3NpemV9IHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiMlwiPlxuICAgIDxjaXJjbGUgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIi8+XG4gICAgPHBvbHlsaW5lIHBvaW50cz1cIjEyLDYgMTIsMTIgMTYsMTRcIi8+XG4gIDwvc3ZnPlxuKVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGF5b3V0RGFzaGJvYXJkIiwiY2xhc3NOYW1lIiwic2l6ZSIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInJlY3QiLCJ4IiwieSIsIlBhY2thZ2UiLCJwYXRoIiwiZCIsIlVzZXJzIiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJVc2VyQ2hlY2siLCJwb2x5bGluZSIsInBvaW50cyIsIkZpbGVUZXh0IiwibGluZSIsIngxIiwieTEiLCJ4MiIsInkyIiwiU2hvcHBpbmdDYXJ0IiwiVHJlbmRpbmdVcCIsIlNldHRpbmdzIiwiTG9nT3V0IiwiV2FyZWhvdXNlIiwiQmFyQ2hhcnQzIiwiUGx1cyIsIlNlYXJjaCIsIkZpbHRlciIsInBvbHlnb24iLCJFZGl0IiwiVHJhc2gyIiwiQWxlcnRUcmlhbmdsZSIsIkJlbGwiLCJVc2VyIiwiTWVudSIsIkRvbGxhclNpZ24iLCJDbG9jayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/icons.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Utility function for combining classes\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(\" \");\n}\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: cn(\"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\.vscode\\\\gestock\\\\gestock-erp\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9nZXN0b2NrLWVycC8uL3NyYy9hcHAvZmF2aWNvbi5pY28/MTEyOSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Finvoicing%2Fcredit-notes%2Fpage&page=%2Finvoicing%2Fcredit-notes%2Fpage&appPaths=%2Finvoicing%2Fcredit-notes%2Fpage&pagePath=private-next-app-dir%2Finvoicing%2Fcredit-notes%2Fpage.tsx&appDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cissam%5C.vscode%5Cgestock%5Cgestock-erp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
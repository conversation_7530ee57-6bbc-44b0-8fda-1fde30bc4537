import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Edit, Trash2 } from '@/components/ui/icons'

const categories = [
  { id: '1', name: 'Informatique', description: 'Ordinateurs, serveurs et équipements IT', products: 45 },
  { id: '2', name: 'Accessoires', description: 'Souris, claviers, casques et périphériques', products: 128 },
  { id: '3', name: '<PERSON><PERSON>rans', description: 'Moniteurs et écrans de toutes tailles', products: 23 },
  { id: '4', name: 'Mobilier', description: 'Bureaux, chaises et mobilier de bureau', products: 67 },
]

export default function CategoriesPage() {
  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Catégories de Produits</h1>
            <p className="text-gray-600">
              Organisez vos produits par catégories pour une meilleure gestion
            </p>
          </div>
          <Button className="flex items-center">
            <Plus className="mr-2 h-4 w-4" />
            Nouvelle catégorie
          </Button>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card key={category.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{category.name}</CardTitle>
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="icon">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{category.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">
                    {category.products} produits
                  </span>
                  <Button variant="outline" size="sm">
                    Voir produits
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats */}
        <Card>
          <CardHeader>
            <CardTitle>Statistiques des Catégories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{categories.length}</p>
                <p className="text-sm text-gray-600">Catégories actives</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {categories.reduce((acc, cat) => acc + cat.products, 0)}
                </p>
                <p className="text-sm text-gray-600">Total produits</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(categories.reduce((acc, cat) => acc + cat.products, 0) / categories.length)}
                </p>
                <p className="text-sm text-gray-600">Moyenne par catégorie</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  {Math.max(...categories.map(cat => cat.products))}
                </p>
                <p className="text-sm text-gray-600">Plus grande catégorie</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
